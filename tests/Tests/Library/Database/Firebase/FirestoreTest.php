<?php

namespace Tests\Library\Database\Firebase;

use AwardForce\Library\Database\Firebase\Claim;
use AwardForce\Library\Database\Firebase\Firestore;
use AwardForce\Library\Database\Firebase\Path;
use AwardForce\Modules\Identity\Users\Models\User;
use DateTime;
use Eloquence\Behaviours\Slug;
use Google\Cloud\Core\Timestamp;
use Google\Cloud\Firestore\DocumentSnapshot;
use Google\Cloud\Firestore\FirestoreClient;
use Illuminate\Support\Facades\Config;
use InvalidArgumentException;
use Kreait\Firebase\Contract\Auth;
use Kreait\Firebase\Contract\Firestore as FirestoreContract;
use Kreait\Firebase\Exception\AuthException;
use Kreait\Firebase\Exception\FirebaseException;
use Kreait\Laravel\Firebase\Facades\Firebase;
use Lcobucci\JWT\UnencryptedToken;
use Mockery\MockInterface;
use Tests\BaseTestCase;
use Tests\Concerns\Laravel;

class FirestoreTest extends BaseTestCase
{
    use Laravel;

    private FirestoreContract|MockInterface $firestoreContract;
    private Firestore $firestore;

    public function init(): void
    {
        $this->firestoreContract = $this->mock(FirestoreContract::class);
        $this->firestore = new Firestore($this->firestoreContract);
    }

    public function testGetMethodReturnsExpectedValue(): void
    {
        $documentSnapshot = $this->mock(DocumentSnapshot::class);
        $documentSnapshot->shouldReceive('get')->with('testFieldPath')->once()->andReturn('testValue');

        $firestoreClientMock = $this->mock(FirestoreClient::class);
        $firestoreClientMock->shouldReceive('collection')->with('collection')->once()->andReturnSelf();
        $firestoreClientMock->shouldReceive('document')->with('testDocumentId')->once()->andReturnSelf();
        $firestoreClientMock->shouldReceive('snapshot')->once()->andReturn($documentSnapshot);

        $this->firestoreContract->shouldReceive('database')->once()->andReturn($firestoreClientMock);

        $this->assertEquals('testValue', $this->firestore->get(new Path('collection', 'testDocumentId', 'testFieldPath')));
    }

    public function testGetMethodReturnsNullWhenDocumentDoesNotExist(): void
    {
        $documentSnapshot = $this->mock(DocumentSnapshot::class);
        $documentSnapshot->shouldReceive('get')->with('testFieldPath')->once()->andThrow(InvalidArgumentException::class);

        $firestoreClientMock = $this->mock(FirestoreClient::class);
        $firestoreClientMock->shouldReceive('collection')->with('collection')->once()->andReturnSelf();
        $firestoreClientMock->shouldReceive('document')->with('testDocumentId')->once()->andReturnSelf();
        $firestoreClientMock->shouldReceive('snapshot')->once()->andReturn($documentSnapshot);

        $this->firestoreContract->shouldReceive('database')->once()->andReturn($firestoreClientMock);

        $this->assertNull($this->firestore->get(new Path('collection', 'testDocumentId', 'testFieldPath')));
    }

    public function testSetMethodDoesNotThrowException(): void
    {
        $firestoreClientMock = $this->mock(FirestoreClient::class);
        $firestoreClientMock->shouldReceive('collection')->with('collection')->once()->andReturnSelf();
        $firestoreClientMock->shouldReceive('document')->with('testDocumentId')->once()->andReturnSelf();
        $firestoreClientMock->shouldReceive('set')->once();

        $this->firestoreContract->shouldReceive('database')->once()->andReturn($firestoreClientMock);

        $this->firestore->set(new Path('collection', 'testDocumentId', 'testFieldPath'), 'testValue');
    }

    public function testPrepareValuesReturnsExpectedArray(): void
    {
        Config::shouldReceive('get')->with('firebase.ttl', null)->once();

        $method = new \ReflectionMethod(Firestore::class, 'prepareValues');

        $result = $method->invoke($this->firestore, 'test.path', 'testValue');

        $this->assertIsArray($result);
        $this->assertArrayHasKey('test', $result);
        $this->assertArrayHasKey('path', $result['test']);
        $this->assertEquals('testValue', $result['test']['path']);
        $this->assertArrayHasKey('expireAt', $result);
        $this->assertInstanceOf(Timestamp::class, $result['expireAt']);
    }

    public function testExpireAt(): void
    {
        $expected = new Timestamp(new DateTime(now()->addSeconds(config('firebase.ttl'))->toIsoString()));

        $this->assertGreaterThanOrEqual($expected, $this->firestore->expireAt());
    }

    /**
     * @throws AuthException
     * @throws FirebaseException
     */
    public function testGenerateAuthTokenReturnsExpectedValue(): void
    {
        $user = new User();
        $user->slug = Slug::random();

        $customClaims = new FakeClaim;

        $token = $this->mock(UnencryptedToken::class);
        $auth = $this->mock(Auth::class);

        $token->shouldReceive('toString')
            ->once()
            ->andReturn($expectedTokenPayload = 'expectedTokenPayload');

        $auth->shouldReceive('createCustomToken')
            ->once()
            ->with((string) $user->slug, $customClaims->get())
            ->andReturn($token);

        Firebase::shouldReceive('auth')
            ->once()
            ->andReturn($auth);

        $this->assertEquals($expectedTokenPayload, $this->firestore->generateAuthToken($user->slug, $customClaims));
    }
}

readonly class FakeClaim implements Claim
{
    public function get(): array
    {
        return [
            'claim1' => 'value1',
            'claim2' => 'value2',
        ];
    }
}
