<?php

namespace Tests\Library\Database\Firebase;

use AwardForce\Library\Database\Firebase\Firestore;
use AwardForce\Library\Database\Firebase\LoggerProxy;
use AwardForce\Library\Database\Firebase\Path;
use Google\Cloud\Firestore\DocumentSnapshot;
use Google\Cloud\Firestore\FirestoreClient;
use Kreait\Firebase\Contract\Firestore as FirestoreContract;
use Mockery\MockInterface;
use Psr\Log\LoggerInterface;
use Tests\BaseTestCase;
use Tests\Concerns\Laravel;

class LoggerProxyTest extends BaseTestCase
{
    use Laravel;

    private FirestoreContract|MockInterface $firestore;
    private LoggerInterface|MockInterface $logger;
    private LoggerProxy $loggerProxy;

    public function init(): void
    {
        $this->firestore = $this->mock(FirestoreContract::class);
        $this->logger = $this->mock(LoggerInterface::class);
        $this->loggerProxy = new LoggerProxy(new Firestore($this->firestore), $this->logger);
    }

    public function testRetrieveDataFromFirestoreLogsTheData(): void
    {
        $path = new Path('collection', 'testDocumentId', 'testFieldPath');
        $result = str_random();

        $documentSnapshot = $this->mock(DocumentSnapshot::class);
        $documentSnapshot->shouldReceive('get')->with('testFieldPath')->once()->andReturn($result);

        $firestoreClientMock = $this->mock(FirestoreClient::class);
        $firestoreClientMock->shouldReceive('collection')->with('collection')->once()->andReturnSelf();
        $firestoreClientMock->shouldReceive('document')->with('testDocumentId')->once()->andReturnSelf();
        $firestoreClientMock->shouldReceive('snapshot')->once()->andReturn($documentSnapshot);

        $this->firestore->shouldReceive('database')->once()->andReturn($firestoreClientMock);
        $this->firestore->shouldReceive('get')->with($path)->andReturn($result);

        $this->logger->shouldReceive('info')->with('Firebase: retrieve data', ['path' => $path->toArray(), 'result' => $result])->once();

        $this->assertEquals($result, $this->loggerProxy->get($path));
    }

    public function testSetDataInFirestoreLogsTheData(): void
    {
        $path = new Path('collection', 'testDocumentId', 'testFieldPath');

        $firestoreClientMock = $this->mock(FirestoreClient::class);
        $firestoreClientMock->shouldReceive('collection')->with('collection')->once()->andReturnSelf();
        $firestoreClientMock->shouldReceive('document')->with('testDocumentId')->once()->andReturnSelf();
        $firestoreClientMock->shouldReceive('set')->once();

        $this->firestore->shouldReceive('database')->once()->andReturn($firestoreClientMock);

        $this->logger->shouldReceive('info')->with('Firebase: set data', ['path' => $path->toArray(), 'value' => 'testValue'])->once();

        $this->loggerProxy->set($path, 'testValue');
    }
}
