<?php

namespace Tests\Library\Search\Actions;

use AwardForce\Library\Search\Actions\ActionOverflowAction;
use AwardForce\Library\Search\Actions\DeleteAction;
use AwardForce\Modules\Categories\Models\Category;

class DeleteAction extends SimpleAction
{
    public function action(): ActionOverflowAction
    {
        return new DeleteAction('category', 'Categories');
    }

    public function record()
    {
        return $this->muffin(Category::class);
    }
}
