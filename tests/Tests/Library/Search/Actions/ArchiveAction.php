<?php

namespace Tests\Library\Search\Actions;

use AwardForce\Library\Search\Actions\ActionOverflowAction;
use AwardForce\Library\Search\Actions\ArchiveAction;
use AwardForce\Modules\Categories\Models\Category;

class ArchiveAction extends SimpleAction
{
    public function action(): ActionOverflowAction
    {
        return new ArchiveAction('entry.manager', 'EntriesAll');
    }

    public function record()
    {
        return $this->muffin(Category::class);
    }
}
