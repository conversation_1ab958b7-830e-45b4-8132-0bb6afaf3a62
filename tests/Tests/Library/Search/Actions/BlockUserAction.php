<?php

namespace Tests\Library\Search\Actions;

use AwardForce\Library\Search\Actions\ActionOverflowAction;
use AwardForce\Library\Search\Actions\BlockUserAction;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Identity\Users\Models\User;

class BlockUserAction extends SimpleAction
{
    public function action(): ActionOverflowAction
    {
        return new BlockUserAction('users', Field::RESOURCE_USERS);
    }

    public function record()
    {
        return $this->muffin(User::class);
    }
}
