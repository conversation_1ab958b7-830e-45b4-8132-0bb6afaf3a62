<?php

namespace Tests\Library\Search\Actions;

use AwardForce\Library\Search\Actions\ActionOverflowAction;
use AwardForce\Library\Search\Actions\ResendReviewTaskNotificationAction;
use AwardForce\Modules\ReviewFlow\Data\ReviewTask;

class ResendReviewTaskNotificationAction extends SimpleAction
{
    public function action(): ActionOverflowAction
    {
        return new ResendReviewTaskNotificationAction('review-flow.task.manage', 'ReviewTask');
    }

    public function record()
    {
        return $this->muffin(ReviewTask::class);
    }
}
