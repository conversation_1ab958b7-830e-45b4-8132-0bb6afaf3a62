<?php

namespace Tests\Library\Search\Actions;

use AwardForce\Library\Search\Actions\ActionOverflowAction;
use AwardForce\Library\Search\Actions\UnrecuseAction;
use AwardForce\Modules\Assignments\Models\Assignment;

class UnrecuseAction extends SimpleAction
{
    public function action(): ActionOverflowAction
    {
        return new UnrecuseAction('assignment', 'EntriesAll');
    }

    public function record()
    {
        return $this->muffin(Assignment::class);
    }
}
