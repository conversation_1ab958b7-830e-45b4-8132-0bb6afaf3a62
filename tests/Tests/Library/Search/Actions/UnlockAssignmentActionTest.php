<?php

namespace Tests\Library\Search\Actions;

use AwardForce\Library\Search\Actions\ActionOverflowAction;
use AwardForce\Library\Search\Actions\UnlockAssignmentAction;
use AwardForce\Modules\Assignments\Models\Assignment;

final class UnlockAssignmentActionTest extends SimpleAction
{
    public function action(): ActionOverflowAction
    {
        return new UnlockAssignmentAction('assignment', 'EntriesAll');
    }

    public function record()
    {
        return $this->muffin(Assignment::class, ['locked' => 1]);
    }

    public function testRender(): void
    {
        $record = $this->record();
        $this->assertStringContainsStringIgnoringCase(route('assignment.unlock', [$record]), $this->action()->render($record));
    }
}
