<?php

namespace Tests\Library\Search\Actions;

use AwardForce\Library\Search\Actions\ActionOverflowAction;
use AwardForce\Library\Search\Actions\ReassignReviewerAction;
use AwardForce\Modules\ReviewFlow\Data\ReviewTask;

class ReassignReviewerActionTest extends SimpleAction
{
    public function action(): ActionOverflowAction
    {
        return new ReassignReviewerAction('review-flow.task.manage', 'EntriesAll');
    }

    public function record()
    {
        return $this->muffin(ReviewTask::class);
    }
}
