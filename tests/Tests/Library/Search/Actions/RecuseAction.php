<?php

namespace Tests\Library\Search\Actions;

use AwardForce\Library\Search\Actions\ActionOverflowAction;
use AwardForce\Library\Search\Actions\RecuseAction;
use AwardForce\Modules\Assignments\Models\Assignment;

class RecuseAction extends SimpleAction
{
    public function action(): ActionOverflowAction
    {
        return new RecuseAction('assignment', 'EntriesAll');
    }

    public function record()
    {
        return $this->muffin(Assignment::class);
    }
}
