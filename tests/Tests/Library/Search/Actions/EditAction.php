<?php

namespace Tests\Library\Search\Actions;

use AwardForce\Library\Search\Actions\ActionOverflowAction;
use AwardForce\Library\Search\Actions\EditAction;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;

class EditAction extends SimpleAction
{
    public function action(): ActionOverflowAction
    {
        return new EditAction('field', 'Fields');
    }

    public function record()
    {
        return $this->muffin(Field::class);
    }
}
