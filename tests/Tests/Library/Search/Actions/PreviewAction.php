<?php

namespace Tests\Library\Search\Actions;

use AwardForce\Library\Search\Actions\ActionOverflowAction;
use AwardForce\Library\Search\Actions\PreviewAction;
use AwardForce\Modules\Entries\Models\Entry;

class PreviewAction extends SimpleAction
{
    public function action(): ActionOverflowAction
    {
        return new PreviewAction('entry.manager', 'EntriesAll');
    }

    public function record()
    {
        return $this->muffin(Entry::class);
    }
}
