<?php

namespace Tests\Library\Search\Actions;

use AwardForce\Library\Search\Actions\ActionOverflowAction;
use AwardForce\Library\Search\Actions\ResetDecisionAction;
use AwardForce\Modules\ReviewFlow\Data\ReviewTask;

class ResetDecisionActionTest extends SimpleAction
{
    public function action(): ActionOverflowAction
    {
        return new ResetDecisionAction('review-flow.task.manage', 'EntriesAll');
    }

    public function record()
    {
        return $this->muffin(ReviewTask::class, [
            'actionTaken' => ReviewTask::ACTION_PROCEED,
        ]);
    }
}
