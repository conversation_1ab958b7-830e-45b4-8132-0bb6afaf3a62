<?php

namespace Tests\Library\Search\Actions;

use AwardForce\Library\Search\Actions\ActionOverflowAction;
use AwardForce\Library\Search\Actions\CopyAction;
use AwardForce\Modules\Categories\Models\Category;

class CopyAction extends SimpleAction
{
    public function action(): ActionOverflowAction
    {
        return new CopyAction('category', 'Categories');
    }

    public function record()
    {
        return $this->muffin(Category::class);
    }
}
