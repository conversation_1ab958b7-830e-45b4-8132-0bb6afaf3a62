<?php

namespace Tests\Library\Search\Actions;

use AwardForce\Library\Search\Actions\ActionOverflowAction;
use AwardForce\Library\Search\Actions\ViewAction;
use AwardForce\Modules\Ecommerce\Orders\Data\Order;

class ViewAction extends SimpleAction
{
    public function action(): ActionOverflowAction
    {
        return new ViewAction('order', 'Orders');
    }

    public function record()
    {
        return $this->muffin(Order::class);
    }
}
