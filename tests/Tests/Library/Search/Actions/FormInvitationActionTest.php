<?php

namespace Tests\Library\Search\Actions;

use AwardForce\Library\Search\Actions\ActionOverflowAction;
use AwardForce\Library\Search\Actions\FormInvitationAction;
use AwardForce\Modules\Categories\Models\Category;
use AwardForce\Modules\Chapters\Models\Chapter;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\Identity\Users\Models\User;
use AwardForce\Modules\Seasons\Facades\SeasonFilter;
use AwardForce\Modules\Seasons\Models\Season;
use PHPUnit\Framework\Attributes\TestWith;

final class FormInvitationActionTest extends SimpleAction
{
    public function action(): ActionOverflowAction
    {
        return new FormInvitationAction('forms', 'Forms');
    }

    public function record()
    {
        return $this->muffin(Form::class);
    }

    public function testForFormMode(): void
    {
        $action = new FormInvitationAction('forms', 'Forms');
        $form = $this->muffin(Form::class);

        $viewData = $action->viewData($form);

        $this->assertArrayNotHasKey('user', $viewData);
        $this->assertArrayHasKey('chapterOption', $viewData);
    }

    public function testForUserMode(): void
    {
        $action = new FormInvitationAction('forms', 'Forms', true);
        $user = $this->muffin(User::class);

        $viewData = $action->viewData($user);

        $this->assertArrayHasKey('user', $viewData);
        $this->assertEquals($user->email, $viewData['user']->email);
        $this->assertArrayNotHasKey('chapterOption', $viewData);
    }

    public function testCategoriesActiveInactiveName(): void
    {
        $inactiveCategory = $this->muffin(Category::class, ['id' => 1, 'active' => false]);
        $activeCategory = $this->muffin(Category::class, ['id' => 2, 'active' => true]);

        $action = new FormInvitationAction('forms', 'Forms');
        $form = $this->muffin(Form::class, ['chapterOption' => Form::CHAPTER_OPTION_ALL]);

        $viewData = $action->viewData($form);

        $this->assertCount(2, $viewData['categories']);
        $collectCategories = collect($viewData['categories']);

        $this->assertEquals(
            trans('entries.form.category.selector.inactive', ['category' => $inactiveCategory->name]),
            $collectCategories->where('id', 1)->first()['name']
        );
        $this->assertEquals(
            $activeCategory->name,
            $collectCategories->where('id', 2)->first()['name']
        );
    }

    #[TestWith([true])]
    #[TestWith([false])]
    public function testFormAlwaysHasAllTheChapters(bool $allSeasons): void
    {
        SeasonFilter::shouldReceive('viewingAll')->andReturn($allSeasons);
        SeasonFilter::shouldReceive('get')->andReturn($this->season);
        SeasonFilter::shouldReceive('getId')->andReturn($this->season->id);
        $inactiveSeason = $this->muffin(Season::class);

        $formA = $this->muffin(Form::class, [
            'chapterOption' => Form::CHAPTER_OPTION_SELECT,
        ]);
        $formB = $this->muffin(Form::class, [
            'chapterOption' => Form::CHAPTER_OPTION_SELECT,
        ]);
        $formC = $this->muffin(Form::class, [
            'chapterOption' => Form::CHAPTER_OPTION_SELECT,
            'season_id' => $inactiveSeason->id,
        ]);

        $chapterA = $this->muffin(Chapter::class);
        $chapterB = $this->muffin(Chapter::class);
        $chapterC = $this->muffin(Chapter::class, ['season_id' => $inactiveSeason->id]);
        $categoryA = $this->muffin(Category::class, ['form_id' => $formA->id]);
        $categoryB = $this->muffin(Category::class, ['form_id' => $formB->id]);
        $categoryC = $this->muffin(Category::class, ['form_id' => $formC->id]);

        $chapterA->forms()->sync($formA->id);
        $chapterB->forms()->sync($formB->id);
        $chapterC->forms()->sync($formC->id);

        $categoryA->chapters()->sync($chapterA);
        $categoryB->chapters()->sync($chapterB);
        $categoryC->chapters()->sync($chapterC);

        $action = new FormInvitationAction('forms', 'Forms');

        $viewData = $action->viewData($formA);
        $this->assertTrue($viewData['chapters']->contains(fn($item) => (int) $item['id'] === $chapterA->id));
        $this->assertTrue($viewData['chapters']->contains(fn($item) => (int) $item['id'] === $chapterB->id));
        $this->assertEquals($allSeasons, $viewData['chapters']->contains(fn($item) => (int) $item['id'] === $chapterC->id));
    }

    public function testOnlyIncludesEntryForms(): void
    {
        $action = new FormInvitationAction('forms', 'Forms');
        $entryForm = $this->muffin(Form::class, ['type' => fn() => Form::FORM_TYPE_ENTRY]);
        $reportForm = $this->muffin(Form::class, ['type' => fn() => Form::FORM_TYPE_REPORT]);

        $viewData = $action->viewData($entryForm);

        $forms = collect($viewData['forms']);

        $this->assertTrue($forms->contains('id', $entryForm->id));
        $this->assertTrue($forms->doesntContain('id', $reportForm->id));
    }
}
