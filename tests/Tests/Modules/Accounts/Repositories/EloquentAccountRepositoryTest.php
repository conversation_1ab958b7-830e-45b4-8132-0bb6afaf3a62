<?php

namespace Tests\Modules\Accounts\Repositories;

use AwardForce\Modules\Accounts\Models\Account;
use AwardForce\Modules\Accounts\Repositories\EloquentAccountRepository;
use AwardForce\Modules\Features\Data\Feature;
use AwardForce\Modules\Files\Models\File;
use AwardForce\Modules\Files\Models\VideoLog;
use AwardForce\Modules\Identity\Users\Models\User;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Config;
use Platform\Features\Feature as PlatformFeature;
use Platform\Language\Language;
use Tests\IntegratedTestCase;

final class EloquentAccountRepositoryTest extends IntegratedTestCase
{
    /** @var EloquentAccountRepository */
    protected $repository;

    public function init()
    {
        $this->repository = App::make(EloquentAccountRepository::class);
    }

    public function testAccountRetrievalByDomain(): void
    {
        $this->account->setVanityDomain('www.someurl.com');

        $account = $this->repository->requireByDomain('www.someurl.com');

        $this->assertNotNull($account);
        $this->assertEquals('www.someurl.com', $account->domains()->first()->domain);
    }

    public function testInvalidDomainRetrieval(): void
    {
        $this->expectException(\AwardForce\Modules\Accounts\AccountNotFoundException::class);

        $this->account->setVanityDomain('www.someurl.com');

        $this->repository->requireByDomain('invalid domain');
    }

    public function testCount(): void
    {
        $initialCount = $this->repository->getCount();

        $account = Account::create([]);

        $this->repository->save($account);

        $account->delete();

        $this->assertSame($initialCount + 1, $this->repository->getCount());
    }

    public function testAddSupportedLanguage(): void
    {
        $this->assertCount(1, $this->account->languages);

        $this->repository->addSupportedLanguage($this->account, new Language('it_IT'));

        $account = $this->account->fresh();

        $this->assertCount(2, $account->languages);
        $this->assertEquals('it_IT', $account->languages->last()->code);
    }

    public function testSyncSupportedLanguages(): void
    {
        $this->assertCount(1, $this->account->languages);

        $this->repository->syncSupportedLanguages($this->account, ['en_GB', 'it_IT'], 'it_IT');

        $account = $this->account->fresh();
        $this->assertCount(2, $account->languages);
        $this->assertEquals('it_IT', $account->defaultLanguage()->code);
    }

    public function testGetAllForActiveRegion(): void
    {
        $accountsInAUBefore = $this->repository->getAllActiveForRegion('au');

        $this->muffins(2, Account::class, ['region' => 'au']);
        $this->muffin(Account::class, ['region' => 'eu']);

        $accountsInAUAfter = $this->repository->getAllActiveForRegion('au');

        $this->assertCount($accountsInAUBefore->count() + 2, $accountsInAUAfter);
    }

    public function testGetByGlobalIdWithRelations(): void
    {
        $account = $this->repository->getByGlobalIdWithRelations($this->account->globalId->toString(), ['owner']);

        $this->assertTrue($account->relationLoaded('owner'));
    }

    public function testGetOrderSummaryWithEmptySeason(): void
    {
        $orderSummary = $this->repository->getOrderSummary($this->account, null);

        $this->assertEmpty($orderSummary);
    }

    public function testGetOrderSummary(): void
    {
        $orderSummary = $this->repository->getOrderSummary($this->account, $this->account->activeSeason());

        $this->assertNotEmpty($orderSummary);
    }

    public function testTotalVideoViewingTime(): void
    {
        $file = $this->muffin(File::class, ['account_id' => $this->account->id]);
        $this->muffin(VideoLog::class, ['file_id' => $file->id]);

        $totalVideoViewingTime = $this->repository->totalVideoViewingTime($this->account);

        $this->assertEquals(10, $totalVideoViewingTime);
    }

    public function testForUser(): void
    {
        $user = $this->muffin(User::class);
        $user->registerMembership($this->account, 'en_GB');

        $results = $this->repository->forUser($user->id);

        $this->assertCount(1, $results);
        $this->assertEquals($this->account->id, $results->first()->id);
    }

    public function testGetManagedBy(): void
    {
        Config::set('awardforce.region', 'au');

        $owner = $this->muffin(User::class);

        $accounts = $this->muffins(3, Account::class);

        foreach ($accounts as $account) {
            $account->setOwner($owner);
            $account->save();
        }

        $results = $this->repository->getManagedBy($owner);

        $this->assertCount(3, $results);
    }

    public function testGetForCursor(): void
    {
        $allAccounts = $this->repository->getAll();
        $allAccountsByCursor = $this->repository->getForCursor();
        $this->assertCount($allAccounts->count(), $allAccountsByCursor);

        $specificAccountsByCursor = $this->repository->getForCursor([
            $allAccounts->first()->id,
        ]);
        $this->assertCount(1, $specificAccountsByCursor);
    }

    public function testRequireBySlug(): void
    {
        $this->expectException(ModelNotFoundException::class);
        $this->repository->requireBySlug('not-slug');

        $account = $this->muffin(Account::class);
        $results = $this->repository->requireBySlug($account->slug);
        $this->assertEquals($account->id, $results->id);
    }

    public function testGetForMissingFeature()
    {
        $this->muffin(Account::class);
        $this->muffin(Account::class);
        $this->muffin(Account::class);
        $feature = new PlatformFeature('mobile_registration_sms', 'enabled');

        $currentCount = $this->repository->getForMissingFeature($feature)->count();

        Feature::add(current_account_id(), $feature, Feature::ENABLED);
        $results = $this->repository->getForMissingFeature($feature);

        // Enabled on current account so it should return the rest
        $this->assertCount($currentCount - 1, $results);
    }
}
