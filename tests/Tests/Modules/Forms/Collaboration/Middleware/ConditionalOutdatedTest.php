<?php

namespace Tests\Modules\Forms\Collaboration\Middleware;

use AwardForce\Http\Middleware\Outdated;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Forms\Collaboration\Middleware\ConditionalOutdated;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use Illuminate\Http\Request;
use Mockery\MockInterface;
use Platform\Http\FormInputOutdatedException;
use Tests\BaseTestCase;
use Tests\Concerns\Laravel;

final class ConditionalOutdatedTest extends BaseTestCase
{
    use Laravel;

    protected Request|MockInterface $request;
    protected Form|MockInterface $form;

    public function init()
    {
        $this->request = $this->mock(Request::class);
        $this->request->shouldReceive('route->parameters')->once()->andReturn([$entry = new Entry]);
        $entry->setRelation('form', $this->form = $this->mock(Form::class));
        $this->form->shouldReceive('supportsRealTimeUpdates')->once()->andReturn(false)->byDefault();
    }

    public function testChecksOutdatedIfFormIsNotCollaborative()
    {
        $this->expectException(FormInputOutdatedException::class);

        (new ConditionalOutdated(new OutdatedStub))->handle($this->request, fn() => null);
    }

    public function testDoesNotChecksOutdatedIfFormIsCollaborative()
    {
        $this->form->shouldReceive('supportsRealTimeUpdates')->once()->andReturn(true);

        (new ConditionalOutdated(new OutdatedStub))->handle($this->request, fn() => null);
    }
}

class OutdatedStub extends Outdated
{
    public function handle($request, $next)
    {
        throw new FormInputOutdatedException('Outdated middleware called');
    }
}
