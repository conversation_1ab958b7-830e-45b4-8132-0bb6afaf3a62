<?php

namespace Tests\Modules\Funding\Rules;

use AwardForce\Library\Values\Amount;
use AwardForce\Library\Values\Currency;
use AwardForce\Modules\AllocationPayments\Models\AllocationPayment;
use AwardForce\Modules\Funding\Data\Allocation;
use AwardForce\Modules\Funding\Rules\MaxAllocationAmount;
use Tests\IntegratedTestCase;

final class MaxAllocationAmountTest extends IntegratedTestCase
{
    public function testFailsIfAllocationAmountIsMoreThanMax(): void
    {
        $allocation = $this->muffin(Allocation::class, [
            'amount' => new Amount(100, new Currency('AUD')),
        ]);

        $rule = new MaxAllocationAmount($allocation, 'paid');

        $this->assertFalse($rule->passes('', 110));
    }

    public function testPassesIfAllocationAmountIsLessThanMax(): void
    {
        $allocation = $this->muffin(Allocation::class, [
            'amount' => new Amount(100, new Currency('AUD')),
        ]);

        $rule = new MaxAllocationAmount($allocation, 'paid');

        $this->assertTrue($rule->passes('', 10));
    }

    public function testPassesIfAllocationHasPaymentsAndAmountIsLessThanPaid(): void
    {
        $allocation = $this->muffin(Allocation::class, [
            'amount' => new Amount(100, new Currency('AUD')),
        ]);
        $this->muffin(AllocationPayment::class, [
            'allocation_id' => $allocation->id,
            'status' => 'paid',
            'amount' => new Amount(50, new Currency('AUD')),
        ]);

        $rule = new MaxAllocationAmount($allocation, 'paid');

        $this->assertTrue($rule->passes('', 10));
    }

    public function testPassesIfAllocationHasPaymentsAndAmountIsGreaterThanPaid(): void
    {
        /** @var Allocation $allocation */
        $allocation = $this->muffin(Allocation::class, [
            'amount' => new Amount(100, new Currency('AUD')),
        ]);
        $this->muffin(AllocationPayment::class, [
            'allocation_id' => $allocation->id,
            'status' => 'paid',
            'amount' => new Amount(20, new Currency('AUD')),
        ]);

        $rule = new MaxAllocationAmount($allocation, 'paid');

        $this->assertTrue($rule->passes('', 40));
    }

    public function testPassesIfAllocationAmountIsEqualToMax(): void
    {
        $allocation = $this->muffin(Allocation::class, [
            'amount' => new Amount(100, new Currency('AUD')),
        ]);

        $allocationPayment = $this->muffin(AllocationPayment::class, [
            'allocation_id' => $allocation->id,
            'status' => 'paid',
            'amount' => new Amount(1, new Currency('AUD')),
        ]);

        $rule = new MaxAllocationAmount($allocation, 'paid', $allocationPayment);

        $this->assertTrue($rule->passes('amount', 100));
    }

    public function testPassesIfUpdatedAmountAmountIsSame(): void
    {
        $allocation = $this->muffin(Allocation::class, [
            'amount' => new Amount(100, new Currency('AUD')),
        ]);

        $allocationPayment = $this->muffin(AllocationPayment::class, [
            'allocation_id' => $allocation->id,
            'status' => 'paid',
            'amount' => new Amount(100, new Currency('AUD')),
        ]);

        $rule = new MaxAllocationAmount($allocation, 'paid', $allocationPayment);

        $this->assertTrue($rule->passes('amount', 100));
    }
}
