<?php

namespace Tests\Modules\AllocationPayments\Commands;

use AwardForce\Modules\AllocationPayments\Commands\AddAllocationPaymentCommand;
use AwardForce\Modules\AllocationPayments\Commands\AddAllocationPaymentCommandHandler;
use AwardForce\Modules\AllocationPayments\Models\AllocationPayment;
use AwardForce\Modules\AllocationPayments\Services\RequestTransformer;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Funding\Data\Allocation;
use AwardForce\Modules\Funding\Data\AllocationRepository;
use AwardForce\Modules\Funding\Data\Fund;
use AwardForce\Modules\PaymentMethods\Models\PaymentMethod;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Mockery as m;
use Tests\IntegratedTestCase;

final class UpdateAllocationPaymentCommandTest extends IntegratedTestCase
{
    use DispatchesJobs;

    private $chapters;

    public function init()
    {
        $this->allocations = m::mock(AllocationRepository::class);
        $this->handler = new AddAllocationPaymentCommandHandler($this->allocations);
    }

    public function testAllocationPaymentIsUpdated(): void
    {
        $fund = $this->muffin(Fund::class);
        $entry = $this->muffin(Entry::class);

        $allocation = $this->muffin(Allocation::class, [
            'fund_id' => $fund->id,
            'entry_id' => $entry->id,
        ]);
        $paymentMethod = $this->muffin(PaymentMethod::class);

        $this->allocations->shouldReceive('getById')->with($allocation->id)->andReturn($allocation);

        /** @var AllocationPayment $allocationPayment */
        $allocationPayment = $this->muffin(AllocationPayment::class, [
            'allocation_id' => $allocation->id,
            'payment_method_id' => $paymentMethod->id,
        ]);

        $requestTransformer = m::mock(RequestTransformer::class);
        $requestTransformer->shouldReceive('fromRequest')->withAnyArgs()
            ->andReturn($data = [
                $paymentMethod->id,
                $allocationPayment->status,
                $reference = '#123456789',
                $allocationPayment->amount->value(),
                $allocation->id,
                $allocationPayment->dateDue,
                $allocationPayment->datePaid,
            ]);

        $allocationPayment = $this->handler->handle(new AddAllocationPaymentCommand(...$data));

        $this->assertInstanceOf(AllocationPayment::class, $allocationPayment);
        $this->assertEquals($reference, $allocationPayment->reference);
    }
}
