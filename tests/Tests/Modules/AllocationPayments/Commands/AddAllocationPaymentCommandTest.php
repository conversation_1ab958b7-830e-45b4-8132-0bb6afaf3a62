<?php

namespace Tests\Modules\AllocationPayments\Commands;

use AwardForce\Modules\AllocationPayments\Commands\AddAllocationPaymentCommand;
use AwardForce\Modules\AllocationPayments\Commands\AddAllocationPaymentCommandHandler;
use AwardForce\Modules\AllocationPayments\Models\AllocationPayment;
use AwardForce\Modules\AllocationPayments\Services\RequestTransformer;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Funding\Data\Allocation;
use AwardForce\Modules\Funding\Data\AllocationRepository;
use AwardForce\Modules\Funding\Data\Fund;
use AwardForce\Modules\PaymentMethods\Models\PaymentMethod;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Mockery as m;
use Tests\IntegratedTestCase;

final class AddAllocationPaymentCommandTest extends IntegratedTestCase
{
    use DispatchesJobs;

    private $chapters;

    public function init()
    {
        $this->allocations = m::mock(AllocationRepository::class);
        $this->handler = new AddAllocationPaymentCommandHandler($this->allocations);
    }

    public function testAllocationPaymentIsCreated(): void
    {
        $fund = $this->muffin(Fund::class);
        $entry = $this->muffin(Entry::class);

        $allocation = $this->muffin(Allocation::class, [
            'fund_id' => $fund->id,
            'entry_id' => $entry->id,
        ]);
        $paymentMethod = $this->muffin(PaymentMethod::class);
        $now = now();

        $this->allocations->shouldReceive('getById')->with($allocation->id)->andReturn($allocation);

        $requestTransformer = m::mock(RequestTransformer::class);
        $requestTransformer->shouldReceive('fromRequest')->withAnyArgs()
            ->andReturn($data = [
                $paymentMethod?->id,
                $status = 'paid',
                $reference = '#123456',
                $amount = 1234,
                $allocation->id,
                $dateDue = $now->copy()->format('Y-m-d'),
                $datePaid = $now->copy()->addDays(2)->format('Y-m-d'),
            ]);

        $allocationPayment = $this->handler->handle(new AddAllocationPaymentCommand(...$data));

        $this->assertInstanceOf(AllocationPayment::class, $allocationPayment);
        $this->assertTrue($allocationPayment->isPaid());
    }
}
