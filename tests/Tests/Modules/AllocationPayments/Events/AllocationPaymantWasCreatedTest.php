<?php

namespace Tests\Modules\AllocationPayments\Events;

use AwardForce\Library\Exceptions\UnsupportedCurrencyException;
use AwardForce\Modules\AllocationPayments\Commands\AddAllocationPaymentCommand;
use AwardForce\Modules\AllocationPayments\Commands\AddAllocationPaymentCommandHandler;
use AwardForce\Modules\AllocationPayments\Events\AllocationPaymentWasCreated;
use AwardForce\Modules\AllocationPayments\Models\AllocationPayment;
use AwardForce\Modules\Funding\Data\Allocation;
use AwardForce\Modules\PaymentMethods\Models\PaymentMethod;
use Illuminate\Support\Facades\Event;
use Tests\IntegratedTestCase;

final class AllocationPaymantWasCreatedTest extends IntegratedTestCase
{
    /**
     * @throws UnsupportedCurrencyException
     */
    public function testAllocationPaymentWasCreatedEventWasDispatched(): void
    {
        Event::fake();
        $paymentMethod = $this->muffin(PaymentMethod::class);
        $allocation = $this->muffin(Allocation::class);
        $allocationPayment = AllocationPayment::add(
            $this->season->id,
            $paymentMethod->id,
            'scheduled',
            '#123456',
            100,
            'EUR',
            $allocation->id,
            $allocation->entryId,
            $allocation->fundId,
            now()->addMinutes(10)->format('Y-m-d'),
            now()->addMinutes(20)->format('Y-m-d'),
        );

        $handler = app(AddAllocationPaymentCommandHandler::class);
        $handler->handle(new AddAllocationPaymentCommand(
            $paymentMethod?->id,
            $allocationPayment->status,
            $allocationPayment->reference,
            $allocationPayment->amount->value(),
            $allocationPayment->allocationId,
            $allocationPayment->getFormattedDateDue(),
            $allocationPayment->getFormattedDatePaid(),
        ));

        Event::assertDispatched(AllocationPaymentWasCreated::class);
    }
}
