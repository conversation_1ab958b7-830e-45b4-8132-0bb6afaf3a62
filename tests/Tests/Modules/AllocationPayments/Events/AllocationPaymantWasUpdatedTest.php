<?php

namespace Tests\Modules\AllocationPayments\Events;

use AwardForce\Library\Exceptions\UnsupportedCurrencyException;
use AwardForce\Modules\AllocationPayments\Commands\UpdateAllocationPaymentCommand;
use AwardForce\Modules\AllocationPayments\Commands\UpdateAllocationPaymentCommandHandler;
use AwardForce\Modules\AllocationPayments\Events\AllocationPaymentWasUpdated;
use AwardForce\Modules\AllocationPayments\Models\AllocationPayment;
use Illuminate\Support\Facades\Event;
use Tests\IntegratedTestCase;

final class AllocationPaymantWasUpdatedTest extends IntegratedTestCase
{
    /**
     * @throws UnsupportedCurrencyException
     */
    public function testAllocationPaymentWasUpdatedEventWasDispatched(): void
    {
        Event::fake();
        $allocationPayment = $this->muffin(AllocationPayment::class);

        $handler = app(UpdateAllocationPaymentCommandHandler::class);
        $handler->handle(new UpdateAllocationPaymentCommand(
            $allocationPayment,
            $allocationPayment->paymentMethod->id,
            $allocationPayment->status,
            $allocationPayment->reference.'22348234hwedfihudsf',
            $allocationPayment->amount->value(),
            $allocationPayment->allocationId,
            $allocationPayment->getFormattedDateDue(),
            $allocationPayment->getFormattedDatePaid(),
        ));

        Event::assertDispatched(AllocationPaymentWasUpdated::class);
    }
}
