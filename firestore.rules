rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // Deny access to all collections by default
    match /{document=**} {
      allow read, write: if false;
    }

    function hasReadAccess() {
      return authenticated() &&
          request.auth.token.submittableAccess.privilege in ['manager', 'owner', 'submitter', 'editor', 'viewer'];
    }

    function hasWriteAccess() {
      return authenticated() &&
          request.auth.token.submittableAccess.privilege in ['manager', 'owner', 'submitter', 'editor', 'viewer'];
    }

    function authenticated() {
      return request.auth != null &&
          request.auth.token != null &&
          request.auth.token.submittableAccess != null;
    }

    function accessAllowed(documentId) {
      return documentId.matches('^' + request.auth.token.submittableAccess.documentPattern + '.*');
    }

    match /attachments/{document} {
      allow read: if hasReadAccess() && accessAllowed(document);
      allow write: if hasWriteAccess() && accessAllowed(document);
    }

    match /collaborators/{document} {
      allow read: if hasReadAccess() && accessAllowed(document);
      allow write: if hasWriteAccess() && accessAllowed(document);
    }

    match /contributors/{document} {
      allow read: if hasReadAccess() && accessAllowed(document);
      allow write: if hasWriteAccess() && accessAllowed(document);
    }

    match /links/{document} {
      allow read: if hasReadAccess() && accessAllowed(document);
      allow write: if hasWriteAccess() && accessAllowed(document);
    }

    match /lockables/{document} {
      allow read: if hasReadAccess() && accessAllowed(document);
      allow write: if hasWriteAccess() && accessAllowed(document);
    }

    match /referees/{document} {
          allow read: if hasReadAccess() && accessAllowed(document);
          allow write: if hasWriteAccess() && accessAllowed(document);
        }

    match /flags-submittable/{document} {
              allow read: if hasReadAccess() && accessAllowed(document);
              allow write: if hasWriteAccess() && accessAllowed(document);
            }
  }
}
