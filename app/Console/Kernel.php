<?php

namespace AwardForce\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [];

    /**
     * The bootstrap classes for the application.
     *
     * @var array
     */
    protected $bootstrappers = [
        \Illuminate\Foundation\Bootstrap\LoadEnvironmentVariables::class,
        \Illuminate\Foundation\Bootstrap\LoadConfiguration::class,
        \AwardForce\Library\Context\Bootstrap::class,
        \Illuminate\Foundation\Bootstrap\HandleExceptions::class,
        \Illuminate\Foundation\Bootstrap\RegisterFacades::class,
        \Illuminate\Foundation\Bootstrap\SetRequestForConsole::class,
        \Illuminate\Foundation\Bootstrap\RegisterProviders::class,
        \Illuminate\Foundation\Bootstrap\BootProviders::class,
    ];

    /**
     * Define the application's command schedule.
     *
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        $schedule->command('uploads:clean')
            ->daily()
            ->withoutOverlapping()
            ->sendOutputTo('/proc/1/fd/1');

        $schedule->command('expire:files')
            ->daily()
            ->withoutOverlapping()
            ->sendOutputTo('/proc/1/fd/1');

        $schedule->command('expire:imports')
            ->daily()
            ->withoutOverlapping()
            ->sendOutputTo('/proc/1/fd/1');

        // $schedule->command('event:clear-logs')
        //         ->weeklyOn(6, '3:00');

        $schedule->command('broadcasts:update-status')
            ->hourly()
            ->withoutOverlapping()
            ->sendOutputTo('/proc/1/fd/1');

        $schedule->command('scheduler:execute-tasks')
            ->everyMinute()
            ->withoutOverlapping()
            ->sendOutputTo('/proc/1/fd/1');

        $schedule->command('files:remove-orphans --verbose --older-than=7')
            ->hourly()
            ->withoutOverlapping()
            ->sendOutputTo('/proc/1/fd/1');

        /*
        $schedule->command('files:purge-deleted --verbose --older-than=30')
            ->daily()
            ->withoutOverlapping()
            ->sendOutputTo('/proc/1/fd/1');
        */
        $schedule->command('horizon:snapshot')
            ->withoutOverlapping()
            ->everyFiveMinutes();

        $schedule->command('cache:prune-stale-tags')
            ->hourly();

        $schedule->command('the-force:prune')
            ->daily()
            ->withoutOverlapping()
            ->sendOutputTo('/proc/1/fd/1');
    }

    /**
     * Register the Closure based commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
