<?php

namespace AwardForce\Modules\Categories\Search\Columns;

use AwardForce\Library\Search\Columns\TranslatedColumnWithFallback;
use AwardForce\Modules\Api\V2\Services\ApiFieldTransformer;
use Platform\Search\ApiColumn;
use Platform\Search\ApiVisibility;
use Platform\Search\Defaults;

class Description extends TranslatedColumnWithFallback implements ApiColumn
{
    use ApiFieldTransformer;

    /**
     * Title for the column - used for headers.
     *
     * @return string|\Illuminate\Contracts\Support\Htmlable
     */
    public function title()
    {
        return trans('category.table.columns.description');
    }

    /**
     * Name of the column. This should be unique.
     */
    public function name(): string
    {
        return 'categories.description';
    }

    /**
     * Returns valid HTML for search list views.
     *
     * @return string|\Illuminate\Contracts\Support\Htmlable
     */
    public function html($record)
    {
        return str_limit($this->value($record), 200);
    }

    /**
     * Returns a Defaults value object that consists of a value representing the views that the column is available on.
     */
    public function default(): Defaults
    {
        return new Defaults('none');
    }

    /**
     * Give columns with particularly important information a higher visibility priority.
     */
    public function priority(): int
    {
        return 500;
    }

    /**
     * The field name is the field the value relates to in the translations table, such as "title" or "description".
     */
    public function fieldName(): string
    {
        return 'description';
    }

    /**
     * By default, translated fields are not sortable, but can be enabled when given enough thought and due diligence.
     */
    public function sortable(): bool
    {
        return false;
    }

    /**
     * Returns attribute name for API output
     *
     * @return string
     */
    public function apiName()
    {
        return 'description';
    }

    /**
     * Returns value formatted for API output
     *
     * @return string
     */
    public function apiValue($record)
    {
        return $this->fetchApiTranslatedField('description', $record);
    }

    /**
     * Returns a ApiVisibility value object that consists of a value representing the views that the column is available on.
     */
    public function apiVisibility(): ApiVisibility
    {
        return new ApiVisibility('all');
    }
}
