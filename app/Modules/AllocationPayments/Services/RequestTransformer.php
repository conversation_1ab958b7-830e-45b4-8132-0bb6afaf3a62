<?php

namespace AwardForce\Modules\AllocationPayments\Services;

use AwardForce\Modules\Funding\Data\Allocation;
use AwardForce\Modules\PaymentMethods\Repositories\PaymentMethodsRepository;
use Illuminate\Http\Request;

class RequestTransformer
{
    public function __construct(protected PaymentMethodsRepository $paymentMethodsRepository)
    {
    }

    public function fromRequest(Request $request, Allocation $allocation): array
    {
        $paymentMethod = $this->paymentMethodsRepository->getById($request->get('payment_method_id'));

        return [
            $paymentMethod?->id,
            $request->get('status'),
            empty($reference = $request->get('reference')) ? null : $reference,
            (float) $request->get('amount'),
            $allocation->id,
            empty($dateDue = $request->get('date_due')) ? null : $dateDue,
            empty($datePaid = $request->get('date_paid')) ? null : $datePaid,
        ];
    }
}
