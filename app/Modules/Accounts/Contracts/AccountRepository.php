<?php

namespace AwardForce\Modules\Accounts\Contracts;

use AwardForce\Modules\Accounts\Models\Account;
use AwardForce\Modules\Accounts\Models\SupportedLanguage;
use AwardForce\Modules\Identity\Users\Models\User;
use AwardForce\Modules\Seasons\Models\Season;
use Illuminate\Support\LazyCollection;
use Platform\Database\Repository;
use Platform\Features\Feature;
use Platform\Language\Language;

interface AccountRepository extends Repository
{
    /**
     * Searches for a domain, should throw an exception if it fails.
     *
     * @return mixed
     */
    public function requireByDomain($domains);

    /**
     * Returns a list of all accounts and the shards they exist on.
     *
     * @return mixed
     */
    public function getByGlobalIdsAcrossShards(array $globalIds);

    /**
     * Count total accounts in each shard.
     *
     * @return mixed
     */
    public function countAllAcrossShards();

    /**
     * Searches for a domain, but simply returns the result.
     *
     * @return mixed
     */
    public function getByDomain($domains);

    /**
     * Return the total number of accounts currently available on the system.
     *
     * @return mixed
     */
    public function getCount();

    /**
     * Returns the active season for the specified account.
     *
     * @return Season
     */
    public function getActiveSeason(Account $account);

    /**
     * Returns the default language code for the requested account.
     *
     * @return mixed
     */
    public function getDefaultLanguage(Account $account);

    /**
     * Add a new language as a supported language of the chosen account.
     *
     * @param  bool  $default
     * @return mixed
     */
    public function addSupportedLanguage(Account $account, Language $language, $default = false): SupportedLanguage;

    /**
     * Returns a collection of supported languages for the account.
     *
     * @return mixed
     */
    public function getSupportedLanguages(Account $account);

    /**
     * Sync the required supported language codes for the account.
     *
     * @param  string  $defaultLanguage
     * @return mixed
     */
    public function syncSupportedLanguages(Account $account, array $languageCodes, $defaultLanguage);

    /**
     * Returns a collection of all active accounts.
     *
     * @return mixed
     */
    public function getAllActiveForRegion(string $region);

    /**
     * Get account by global id with relations for Falcon
     *
     * @param  array  $relations
     * @return mixed
     */
    public function getByGlobalIdWithRelations(string $globalId, $relations = []);

    /**
     * Get account order information
     *
     * @return mixed
     */
    public function getOrderSummary(Account $account, ?Season $season);

    /**
     * Total video viewing time for Falcon
     */
    public function totalVideoViewingTime(Account $account): int;

    /**
     * Get account by its global id
     *
     * @return mixed
     */
    public function getByGlobalId(string $globalId);

    /**
     * The accounts in the current region where the given user is the owner or a program manager.
     *
     * @return mixed
     */
    public function getManagedBy(User $user);

    /**
     * Gets first ( any ) unsuspended account with with existing owner
     */
    public function getFirstActiveAccount();

    /**
     * Returns a lazy collection of models given an optional array of IDs.
     */
    public function getForCursor(array $ids = []): LazyCollection;

    public function getForMissingFeature(Feature $feature): LazyCollection;
}
