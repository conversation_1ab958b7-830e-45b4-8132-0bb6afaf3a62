<?php

namespace AwardForce\Modules\Accounts\Repositories;

use AwardForce\Library\Authorization\Consumer;
use AwardForce\Library\Cache\Cacher;
use AwardForce\Library\Database\Eloquent\Repository;
use AwardForce\Library\Region\RegionSelector;
use AwardForce\Modules\Accounts\AccountNotFoundException;
use AwardForce\Modules\Accounts\Contracts\AccountRepository;
use AwardForce\Modules\Accounts\Models\Account;
use AwardForce\Modules\Accounts\Models\Domain;
use AwardForce\Modules\Accounts\Models\SupportedLanguage;
use AwardForce\Modules\Identity\Roles\Contracts\PermissionRepository;
use AwardForce\Modules\Identity\Users\Models\User;
use AwardForce\Modules\Seasons\Models\Season;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\LazyCollection;
use Platform\Authorisation\FeatureRoles\ProgramManager;
use Platform\Database\Selector;
use Platform\Features\Feature;
use Platform\Language\Language;

class EloquentAccountRepository extends Repository implements AccountRepository
{
    use Cacher;

    /**
     * Accounts are the top-level root domain of the entire system. Therefore, they are removed
     * from the default account restriction for querying.
     *
     * @var bool
     */
    public $restrictByAccount = false;

    /**
     * Make sure we assign the required model.
     */
    public function __construct(Account $model)
    {
        $this->model = $model;
    }

    /**
     * Require an account based on the domain that has been provided. If no account is found,
     * an AccountNotFoundException is thrown.
     *
     * @param  string  $domain
     * @return array
     *
     * @throws AccountNotFoundException
     */
    public function requireByDomain($domain)
    {
        $account = $this->getByDomain($domain);

        if (! $account) {
            throw new AccountNotFoundException("An account for domain [{$domain}] could not be found.");
        }

        return $account;
    }

    /**
     * Searches for an account based on the domain provided.
     *
     * @return mixed
     */
    public function getByDomain($domain)
    {
        $accountId = $this->globalCache("byDomain:{$domain}", 'domain', function () use ($domain) {
            return Domain::whereDomain($domain)->first()->accountId ?? null;
        });

        return $accountId ? Account::find($accountId) : null;
    }

    /**
     * Retrieves the total count for the number of accounts added to the system (both deleted and not).
     *
     * @return int
     */
    public function getCount()
    {
        return Account::withTrashed()->count();
    }

    /**
     * Returns the active season for the specified account.
     *
     * @return Season
     */
    public function getActiveSeason(Account $account)
    {
        return $account->activeSeason();
    }

    /**
     * Returns the default language code for the requested account.
     *
     * @return mixed
     */
    public function getDefaultLanguage(Account $account)
    {
        return $account->languages()->where('default', '=', 1)->first();
    }

    /**
     * Add a new language as a supported language of the chosen account.
     *
     * @param  bool  $default
     * @return mixed
     */
    public function addSupportedLanguage(Account $account, Language $language, $default = false): SupportedLanguage
    {
        return $account->languages()->save(new SupportedLanguage(['code' => $language->code, 'default' => $default]));
    }

    /**
     * Returns a collection of supported languages for the account.
     *
     * @return mixed
     */
    public function getSupportedLanguages(Account $account)
    {
        return $account->languages;
    }

    /**
     * Sync the required supported language codes for the account.
     *
     * @param  string  $defaultLanguage
     * @return mixed
     */
    public function syncSupportedLanguages(Account $account, array $languageCodes, $defaultLanguage)
    {
        $account->languages()->delete();

        if (is_null($defaultLanguage)) {
            $defaultLanguage = reset($languageCodes);
        }

        foreach ($languageCodes as $code) {
            $this->addSupportedLanguage($account, new Language($code), $code == $defaultLanguage);
        }
    }

    /**
     * Returns the active consumer.
     */
    protected function consumer(): Consumer
    {
        return Consumer::get();
    }

    /**
     * Returns a collection of all active accounts.
     *
     * @return mixed
     */
    public function getAllActiveForRegion(string $region)
    {
        return $this->getQuery()
            ->whereSuspended(false)
            ->whereRegion($region)
            ->get();
    }

    public function getByGlobalIdWithRelations(string $globalId, $relations = [])
    {
        return $this->getQuery()
            ->where('global_id', $globalId)
            ->with($relations)
            ->first();
    }

    /**
     * Get account order information
     *
     * @return mixed
     */
    public function getOrderSummary(Account $account, ?Season $season)
    {
        if (! $season) {
            return collect();
        }

        $subQuery = DB::raw("(
            SELECT currency_code, SUM(total) as total, COUNT(*) as count
            FROM orders
            WHERE season_id = {$season->id}
            GROUP BY currency_code) as orders
        ");

        return $this->getConnection()
            ->table('supported_currencies')
            ->select(DB::raw('supported_currencies.code, COALESCE(orders.total,0) as total, COALESCE(orders.count,0) as quantity'))
            ->leftJoin($subQuery, 'supported_currencies.code', '=', 'orders.currency_code')
            ->where('supported_currencies.account_id', '=', $account->id)
            ->get();
    }

    /**
     * Total video viewing time for Falcon
     */
    public function totalVideoViewingTime(Account $account): int
    {
        $total = $this->getConnection()
            ->table('files')
            ->where('files.account_id', $account->id)
            ->join('video_logs', 'video_logs.file_id', '=', 'files.id')
            ->sum('video_logs.watched');

        return (int) $total;
    }

    public function forUser(int $userId)
    {
        return $this->getQuery()
            ->select('accounts.id', 'accounts.global_id', 'accounts.slug', 'accounts.user_id', \DB::raw('COUNT(entries.id) AS total_entries'), 'memberships.deleted_at')
            ->join('memberships', 'memberships.account_id', '=', 'accounts.id')
            ->leftJoin('entries', function ($join) use ($userId) {
                $join->on('entries.account_id', '=', 'accounts.id');
                $join->where('entries.user_id', '=', $userId);
            })
            ->where('memberships.user_id', '=', $userId)
            ->groupBy('accounts.id')
            ->with('owner')
            ->get();
    }

    /**
     * Get account by its global id
     *
     * @return mixed
     */
    public function getByGlobalId(string $globalId)
    {
        return $this->getBy('global_id', $globalId)->first();
    }

    /**
     * The accounts in the current region where the given user is the owner or a program manager.
     *
     * @return mixed
     */
    public function getManagedBy(User $user)
    {
        return $this->getQuery()
            ->select('accounts.id', 'accounts.global_id', 'accounts.user_id')
            ->leftJoin('memberships', 'memberships.account_id', '=', 'accounts.id')
            ->where('region', '=', app(RegionSelector::class)->current()->name())
            ->where(function ($query) use ($user) {
                $query->where('accounts.user_id', '=', $user->id);
                $query->orWhere('memberships.user_id', '=', $user->id);
            })
            ->groupBy('accounts.id')
            ->get()
            ->filter(function (Account $account) use ($user) {
                if ($account->userId == $user->id) {
                    return true;
                }

                $permissions = app(PermissionRepository::class)->getByAccountAndUserId($account->id, $user->id);

                return ProgramManager::appliesTo($permissions);
            })
            ->mapWithKeys(function (Account $account) {
                $account = translate($account);

                return [(string) $account->globalId => $account->getTranslation('name', $account->defaultLanguage()->code())];
            })
            ->sort();
    }

    public function getByGlobalIdsAcrossShards(array $globalIds)
    {
        $databases = ($dbSelector = app(Selector::class))->all();

        $accounts = [];

        foreach ($databases as $database) {
            $dbSelector->select($database);

            $accounts[$database->name()] = $this->getQuery()->get()->keyBy('global_id');
        }

        return collect($accounts);
    }

    /**
     * Count total accounts in each shard.
     *
     * @return mixed
     */
    public function countAllAcrossShards()
    {
        $databases = ($dbSelector = app(Selector::class))->all();

        $accounts = [];

        foreach ($databases as $database) {
            $dbSelector->select($database);

            $accounts[$database->name()] = $this->getQuery()->count();
        }

        return collect($accounts);
    }

    /**
     * Gets first ( any ) unsuspended account with with existing owner
     */
    public function getFirstActiveAccount()
    {
        return $this->getQuery()
            ->whereNotNull('user_id')
            ->whereSuspended(0)
            ->first();
    }

    /**
     * Returns a lazy collection of models given an optional array of IDs.
     */
    public function getForCursor(array $ids = []): LazyCollection
    {
        return $this->getQuery()
            ->when(! empty($ids), fn(Builder $query) => $query->whereIn('id', $ids))
            ->cursor();
    }

    public function getForMissingFeature(Feature $feature): LazyCollection
    {
        return $this->getQuery()
            ->whereNotExists(function ($query) use ($feature) {
                $query->select(\DB::raw(1))
                    ->from('features')
                    ->whereRaw('features.account_id = accounts.id')
                    ->where('features.feature', '=', $feature->value())
                    ->where('features.status', '=', $feature->status());
            })
            ->cursor();
    }
}
