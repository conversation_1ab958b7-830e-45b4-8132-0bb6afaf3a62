<?php

namespace AwardForce\Modules\Documents\Search\Columns;

use AwardForce\Library\Search\Columns\TranslatedColumnWithFallback;
use AwardForce\Modules\Api\V2\Services\ApiFieldTransformer;
use Illuminate\Support\Collection;
use Platform\Search\ApiColumn;
use Platform\Search\ApiVisibility;
use Platform\Search\Defaults;
use Platform\Search\Filters\IncludeFilter;

class Description extends TranslatedColumnWithFallback implements ApiColumn
{
    use ApiFieldTransformer;

    public function title(): string
    {
        return trans('documents.table.columns.description');
    }

    public function name(): string
    {
        return 'document_template.description';
    }

    public function dependencies(): Collection
    {
        return collect([
            IncludeFilter::class,
        ]);
    }

    public function value($record): ?string
    {
        return lang($record->documentTemplate, 'description', emptyString: '—');
    }

    public function html($record): string
    {
        return str_limit($this->value($record) ?? '—', 200);
    }

    public function default(): Defaults
    {
        return new Defaults('none');
    }

    public function priority(): int
    {
        return 90;
    }

    public function fieldName(): string
    {
        return 'description';
    }

    public function apiName(): string
    {
        return 'description';
    }

    public function apiValue($record): string|Collection
    {
        return $this->fetchApiTranslatedField('description', $record->documentTemplate);
    }

    public function apiVisibility(): ApiVisibility
    {
        return new ApiVisibility('all');
    }
}
