<?php

namespace AwardForce\Modules\Billing;

use AwardForce\Library\Providers\Providable;
use AwardForce\Library\ServiceProvider;
use AwardForce\Modules\PaymentSubscriptions\Events\SubscriptionWasProcessed;

class BillingServiceProvider extends ServiceProvider
{
    use Providable;

    /**
     * Define the listeners for this module.
     *
     * @var array
     */
    protected $listeners = [
        SubscriptionWasProcessed::class => 'AwardForce\Modules\Billing\Listeners\BillingListener@whenSubscriptionWasProcessed',
    ];
}
