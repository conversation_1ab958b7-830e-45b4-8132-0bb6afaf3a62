<?php

namespace AwardForce\Modules\Judging\Search\Leaderboard;

use AwardForce\Library\Search\Filters\SeasonalFilter;
use AwardForce\Library\Search\Filters\TagSearchFilter;
use AwardForce\Library\Search\SeasonalColumnator;
use AwardForce\Modules\Assignments\Models\AssignmentRepository;
use AwardForce\Modules\Entries\Contracts\EntryRepository;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Entries\Search\Columns\Division;
use AwardForce\Modules\Entries\Search\Columns\EntryFundAllocations;
use AwardForce\Modules\Entries\Search\Filters\CategorySearchFilter;
use AwardForce\Modules\Entries\Search\Filters\ChapterSearchFilter;
use AwardForce\Modules\Entries\Search\Filters\Contributors\EntrySlugFilter;
use AwardForce\Modules\Entries\Search\Filters\EntryStateFilter;
use AwardForce\Modules\Entries\Search\Filters\FieldColumnOrderFilter;
use AwardForce\Modules\Files\Services\Thumbnails\ThumbnailRetriever;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Fields\Database\Repositories\FieldRepository;
use AwardForce\Modules\Forms\Fields\Search\Columns\Search\FieldAutoScore;
use AwardForce\Modules\Forms\Fields\Search\Columns\Search\TotalScoreColumn;
use AwardForce\Modules\Forms\Fields\Search\Columns\SearchField;
use AwardForce\Modules\Forms\Fields\Search\Enhancers\ScoreSetFieldValuesEnhancer;
use AwardForce\Modules\Funding\Data\Allocation;
use AwardForce\Modules\GrantReports\Search\Columns\EntryStatus;
use AwardForce\Modules\Judging\Search\Columns\GrantStatus;
use AwardForce\Modules\Judging\Search\Filters\AssignmentEntryFilter;
use AwardForce\Modules\Judging\Search\Filters\AssignmentUserFilter;
use AwardForce\Modules\Judging\Search\Filters\ChapterManagerRoleFilter;
use AwardForce\Modules\Judging\Search\Filters\CompletedAssignmentsFilter;
use AwardForce\Modules\Judging\Search\Filters\HideRecusedAssignmentsFilter;
use AwardForce\Modules\Judging\Search\Filters\HideStrayAssignmentsFilter;
use AwardForce\Modules\Judging\Search\Filters\ScoreSetFilter;
use AwardForce\Modules\Judging\Search\Leaderboard\Columns\ActionOverflow;
use AwardForce\Modules\Judging\Search\Leaderboard\Columns\Category;
use AwardForce\Modules\Judging\Search\Leaderboard\Columns\CategoryShortcode;
use AwardForce\Modules\Judging\Search\Leaderboard\Columns\CategorySlug;
use AwardForce\Modules\Judging\Search\Leaderboard\Columns\Chapter;
use AwardForce\Modules\Judging\Search\Leaderboard\Columns\ChapterSlug;
use AwardForce\Modules\Judging\Search\Leaderboard\Columns\Created;
use AwardForce\Modules\Judging\Search\Leaderboard\Columns\EntrantEmail;
use AwardForce\Modules\Judging\Search\Leaderboard\Columns\EntrantFirstName;
use AwardForce\Modules\Judging\Search\Leaderboard\Columns\EntrantLastName;
use AwardForce\Modules\Judging\Search\Leaderboard\Columns\EntrantSlug;
use AwardForce\Modules\Judging\Search\Leaderboard\Columns\EntryId;
use AwardForce\Modules\Judging\Search\Leaderboard\Columns\EntryLink;
use AwardForce\Modules\Judging\Search\Leaderboard\Columns\EntrySlug;
use AwardForce\Modules\Judging\Search\Leaderboard\Columns\EntryTitle;
use AwardForce\Modules\Judging\Search\Leaderboard\Columns\LeaderboardMarker;
use AwardForce\Modules\Judging\Search\Leaderboard\Columns\LocalId;
use AwardForce\Modules\Judging\Search\Leaderboard\Columns\ParentCategory;
use AwardForce\Modules\Judging\Search\Leaderboard\Columns\ScoreSetName;
use AwardForce\Modules\Judging\Search\Leaderboard\Columns\ScoreSetSlug;
use AwardForce\Modules\Judging\Search\Leaderboard\Columns\SubmittedAt;
use AwardForce\Modules\Judging\Search\Leaderboard\Columns\Tags;
use AwardForce\Modules\Judging\Search\Leaderboard\Columns\Thumbnail;
use AwardForce\Modules\Judging\Search\Leaderboard\Columns\Updated;
use AwardForce\Modules\Judging\Search\Leaderboard\Enhancers\Thumbnails;
use AwardForce\Modules\Judging\Search\Leaderboard\Filters\GrantStatusFilter;
use AwardForce\Modules\Judging\Search\Leaderboard\Filters\ScoreSetJoinFilter;
use AwardForce\Modules\ScoreSets\Models\ScoreSet;
use AwardForce\Modules\ScoreSets\Models\ScoreSetRepository;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Session;
use Platform\Database\Eloquent\Repository;
use Platform\Search\Columnator;
use Platform\Search\Columns;
use Platform\Search\Defaults;
use Platform\Search\Dependencies;
use Platform\Search\Filters\ColumnFilter;
use Platform\Search\Filters\GroupingFilter;
use Platform\Search\Filters\IncludeFilter;
use Platform\Search\Filters\KeywordFilter;
use Platform\Search\Filters\PaginationFilter;
use Platform\Search\Filters\TranslatedColumnSearchFilter;

abstract class LeaderboardColumnator extends Columnator
{
    use SeasonalColumnator;

    const ENTRY_LIST_LENGTH_DEFAULT = 10;

    /** @var ScoreSet */
    protected $scoreSet;

    /** @var Collection */
    private $fieldColumns;

    abstract protected function mode(): string;

    protected function baseColumns()
    {
        return (new Columns([
            new LeaderboardMarker,
            new ActionOverflow($this->key()),
            new Thumbnail('entry.manager.view'),
            new LocalId,
            new EntryId,
            new CategoryShortcode,
            new EntrySlug,
            new EntryTitle,
            new Tags,
        ]))->merge($this->priorityColumns())->merge([
            new ChapterSlug,
            new Chapter,
            new CategorySlug,
            new ParentCategory,
            new Category,
            new Division,
            new EntrantSlug,
            new EntrantFirstName,
            new EntrantLastName,
            new EntrantEmail,
            new ScoreSetName($scoreSet = $this->scoreSet()),
            new ScoreSetSlug($scoreSet),
            new GrantStatus(feature_enabled('grants') && \Consumer::can('view', 'Grants')),
            new Created,
            new Updated,
            new SubmittedAt,
            new EntryFundAllocations,
            new TotalScoreColumn,
            new EntryStatus,
            new EntryLink,
        ])->merge($this->modeColumns());
    }

    protected function priorityColumns(): Columns
    {
        return new Columns;
    }

    protected function modeColumns(): Columns
    {
        return new Columns;
    }

    protected function fieldColumns()
    {
        return $this->fieldColumns ?: $this->fieldColumns = collect(
            array_merge(
                $this->fields(Field::RESOURCE_FORMS, 'entries.id'),
                $this->fields(Field::RESOURCE_USERS, 'entries.user_id')
            )
        );
    }

    protected function fields(string $resource, string $foreignId): array
    {
        $fields = app(FieldRepository::class)
            ->getByResourceWhereFieldTypeNotAndFormTypeIsNotReport($resource, ['content', 'table'], $this->seasonId())
            ->translate();

        return [
            ...$fields->map(fn(Field $field) => SearchField::fromField($field, $foreignId, new Defaults('none')))->all(),
            ...$fields->filter(fn(Field $field) => $field->autoScoring)->map(fn(Field $field) => new FieldAutoScore($field))->all(),
        ];
    }

    public function availableDependencies(Defaults $view): Dependencies
    {
        $columns = $this->columns($view);
        $dependencies = new Dependencies;

        // Filters
        $dependencies->add(new AssignmentEntryFilter($this->input, true));
        $dependencies->add(new AssignmentUserFilter());

        $dependencies->add((new ColumnFilter(...$columns))->with('assignments.id', 'assignments.entry_id', 'assignments.judge_id', 'assignments.score_set_id', 'entries.user_id as entrant_id'));
        $dependencies->add(new IncludeFilter(['judge', 'judge.currentMembership', 'entry.allocations', 'entry.chapter', 'entry.category.parent', 'entry.entrant.currentMembership', 'entry.tags', 'entry.allocations', 'entry.form:id,type', 'scoreSet']));
        $dependencies->add(app(ChapterManagerRoleFilter::class));
        $dependencies->add(app(CompletedAssignmentsFilter::class));
        $dependencies->add(app(HideStrayAssignmentsFilter::class));
        $dependencies->add(app(HideRecusedAssignmentsFilter::class));
        $dependencies->add(new ChapterSearchFilter($this->input));
        $dependencies->add(new CategorySearchFilter($this->input, $divisions = true));
        $dependencies->add(new TagSearchFilter($this->input, app(EntryRepository::class), [Entry::class, Allocation::class], [Allocation::class => 'entry_id']));
        $dependencies->add(new EntryStateFilter($this->input));
        $dependencies->add(new EntrySlugFilter($this->input));
        $dependencies->add(new SeasonalFilter(array_get($this->input, 'season'), 'assignments.season_id'));
        $dependencies->add(new ScoreSetFilter($this->input, app(ScoreSetRepository::class)));
        $dependencies->add(new ScoreSetJoinFilter());
        $dependencies->when(
            $this->hasGrantAccess(),
            fn($dependency) => $dependency->add(new GrantStatusFilter($this->input, false))
        );
        $dependencies->add(new GroupingFilter('assignments.entry_id'));
        $dependencies->add(new PaginationFilter($this->input, $this->entryListLength()));
        $dependencies->add(KeywordFilter::fromInput($this->input, ['entries.title', 'entries.local_id']));
        $dependencies->add($this->orderFilter($columns, $this->input));

        $dependencies->add((new TranslatedColumnSearchFilter($columns->translatedColumns('leaderboard.chapter'), 'Chapter', current_account_id()))
            ->setJoinTable('chapters')->restrictLanguage($language = consumer()->languageCode()));
        $dependencies->add((new TranslatedColumnSearchFilter($columns->translatedColumns(['leaderboard.category', 'leaderboard.category_shortcode']), 'Category', current_account_id()))
            ->setJoinTable('categories')->restrictLanguage($language));
        $dependencies->add((new TranslatedColumnSearchFilter($columns->translatedColumns('grant_status'), 'GrantStatus', current_account_id()))->setJoinTable('grant_statuses')->restrictLanguage($language));
        // Enhancers
        $dependencies->add(new Thumbnails(app(ThumbnailRetriever::class), $this->scoreSet()));
        $dependencies->add(ScoreSetFieldValuesEnhancer::fromColumns($columns));

        return $dependencies->merge($this->modeDependencies($view));
    }

    protected function orderFilter(Columns $columns, array $input)
    {
        return FieldColumnOrderFilter::fromColumns($columns, $input, 'leaderboard.entry-title')->uniqueColumn('assignments.id');
    }

    protected function modeDependencies(Defaults $view): Dependencies
    {
        return new Dependencies;
    }

    public function resource()
    {
        return ['ScoresAll', 'EntriesAll'];
    }

    public function repository(): Repository
    {
        return app(AssignmentRepository::class);
    }

    protected function scoreSet()
    {
        if ($this->scoreSet) {
            return $this->scoreSet;
        }

        if ($scoreSetId = $this->input['score-set'] ?? Session::get('leaderboard-score-set')) {
            return $this->scoreSet = translate(app(ScoreSetRepository::class)->getById($scoreSetId));
        }

        if ($scoreSetSlug = $this->input['score_set'] ?? null) {
            return $this->scoreSet = translate(app(ScoreSetRepository::class)->getBySlug($scoreSetSlug));
        }

        return new ScoreSet;
    }

    private function entryListLength(): int
    {
        return $this->scoreSet()->entryListLength ?? self::ENTRY_LIST_LENGTH_DEFAULT;
    }

    private function hasGrantAccess(): bool
    {
        return feature_enabled('grants') && (\Consumer::can('view', 'Grants') || is_api_consumer());
    }
}
