<?php

namespace AwardForce\Modules\Entries\Commands;

use AwardForce\Library\Database\Firebase\Database;
use AwardForce\Library\Encrypter\Strategies\Encrypter;
use AwardForce\Modules\Forms\Collaboration\Firebase\Locator\FlagsSubmittable;

class UpdateCollaborationFlagsSubmittableHandler
{
    public function __construct(
        protected Encrypter $encrypter,
        protected Database $database
    ) {
    }

    public function handle(UpdateCollaborationFlagsSubmittable $command)
    {
        if (! $command->entry->form->settings->collaborative) {
            return;
        }

        $data = [
            'isIneligible' => $command->entry->isIneligible(),
        ];

        $this->database->set(
            (new FlagsSubmittable($command->entry))->path('data.encrypted'),
            $this->encrypter->encrypt($data, $command->entry),
        );
    }
}
