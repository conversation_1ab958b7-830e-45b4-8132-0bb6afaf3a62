<?php

namespace AwardForce\Modules\Entries\Commands;

use AwardForce\Library\Database\Firebase\Database as FirebaseDatabase;
use AwardForce\Library\Encrypter\Strategies\Encrypter;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\Forms\Forms\Database\Entities\FormSettings;
use Mockery as m;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

final class UpdateCollaborationFlagsSubmittableTest extends BaseTestCase
{
    use Database;
    use Laravel;

    /**
     * Dependencies for the test.
     *
     * @var FirebaseDatabase
     * @var UpdateCollaborationFlagsSubmittableHandler
     * @var Entry
     * @var Form
     */
    protected Encrypter $encrypter;

    protected FirebaseDatabase $database;
    protected UpdateCollaborationFlagsSubmittableHandler $handler;
    protected Entry $entry;
    protected Form $form;

    public function init()
    {
        $this->encrypter = m::spy(Encrypter::class);
        $this->database = m::spy(FirebaseDatabase::class);

        $this->handler = new UpdateCollaborationFlagsSubmittableHandler($this->encrypter, $this->database);

        $this->form = $this->muffin(Form::class, ['settings' => FormSettings::create(['collaborative' => false])]);

        $this->entry = $this->muffin(Entry::class, [
            'form_id' => $this->form->id,
        ]);
    }

    public function testDontSetFirebaseDataIfFormIsNotCollaborative(): void
    {
        $command = new UpdateCollaborationFlagsSubmittable($this->entry);
        $this->handler->handle($command);

        $this->database->shouldnotHaveReceived('set');
    }

    public function testSetFirebaseDataIfFormIsCollaborative(): void
    {
        $this->form->settings = FormSettings::create(['collaborative' => true]);
        $this->form->save();

        $command = new UpdateCollaborationFlagsSubmittable($this->entry);
        $this->handler->handle($command);

        $this->database->shouldHaveReceived('set')->once();
        $this->encrypter->shouldHaveReceived('encrypt')->once();
    }
}
