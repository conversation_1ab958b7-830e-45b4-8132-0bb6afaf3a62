<?php

namespace AwardForce\Modules\Files\Services;

use AwardForce\Modules\Accounts\Models\Account;
use AwardForce\Modules\Files\Contracts\Transcoder;
use AwardForce\Modules\Files\Events\TranscodingJobWasQueued;
use AwardForce\Modules\Files\Models\File;
use AwardForce\Modules\Files\Models\Transcode;
use Aws\MediaConvert\MediaConvertClient;
use Illuminate\Support\Facades\Log;
use Platform\Events\Raiseable;

class AWSMediaConvert implements Transcoder
{
    const DEFAULT_MANIFEST = 'file.m3u8';

    const DEFAULT_THUMBNAIL = 'file-thumb.0000000.jpg';

    const STATUS_COMPLETED = 'COMPLETE';

    /**
     * Legacy AWSElasticTranscoder default manifest
     */
    const LEGACY_MANIFEST = 'playlist.m3u8';

    /**
     * Legacy AWSElasticTranscoder default thumbnail
     */
    const LEGACY_THUMBNAIL = 'thumb-00001.png';

    use Raiseable;

    /**
     * Extracts the output details from the response received from AWS Media Convert or the error code if transcoding failed.
     */
    public static function outputDetails(array $message): array
    {
        // if output details are available, return them
        if ($outputs = $message['outputGroupDetails'] ?? false) {
            return $outputs;
        }

        // otherwise look for an error code and message
        if (($errorCode = $message['errorCode'] ?? false) && ($errorMessage = $message['errorMessage'] ?? false)) {
            return ['errorCode' => $errorCode, 'errorMessage' => $errorMessage];
        }

        return [];
    }

    /**
     * Checks if the provided outputs were generated by AWS Media Convert.
     */
    public static function compatibleOutputs(array $outputs): bool
    {
        return ! empty($outputs[0]->outputDetails) || ($outputs['errorCode'] ?? false);
    }

    /**
     * Extracts the duration of the transcoded file from one of the outputs.
     */
    public static function outputDuration(array $outputs): int
    {
        return floor(($outputs[0]->outputDetails[0]->durationInMs ?? 0) / 1000);
    }

    public function transcode(Transcode $transcode, File $file)
    {
        Log::info('Sending transcoding payload to AWS Media Convert', $payload = $this->payload($transcode, $file));

        $this->client($file->account)->createJob($payload);

        $this->raise(new TranscodingJobWasQueued($transcode, $file));
    }

    private function payload(Transcode $transcode, File $file): array
    {
        return [
            'JobTemplate' => $template = $this->jobTemplate($file->account),
            'Role' => config("services.aws.media_convert.{$file->account->region}.role"),
            'Settings' => [
                'Inputs' => [
                    [
                        'FileInput' => ($bucket = config("services.aws.media_convert.{$file->account->region}.bucket")).'/'.$file->file,
                    ],
                ],
                'OutputGroups' => $this->outputGroups($file, $bucket, $template),
            ],
            'UserMetadata' => [
                'transcodeId' => (string) $transcode->id,
                'fileId' => (string) $file->id,
                'globalAccountId' => (string) $file->account->globalId,
                'userId' => (string) $transcode->userId,
                'environment' => config('app.env'),
            ],
        ];
    }

    private function jobTemplate(Account $account): string
    {
        if ($customTemplate = $account->transcodingTemplate) {
            return $customTemplate;
        }

        return config("services.aws.media_convert.{$account->region}.template");
    }

    private function outputGroups(File $file, string $bucket, string $template): array
    {
        $jobTemplate = $this->client($file->account)->getJobTemplate(['Name' => $template]);

        $outputBucket = $bucket.'/'.pathinfo($file->file, PATHINFO_DIRNAME).'/';

        return collect($jobTemplate['JobTemplate']['Settings']['OutputGroups'])
            ->map(function ($outputGroup) use ($outputBucket) {
                match ($outputGroup['OutputGroupSettings']['Type']) {
                    'FILE_GROUP_SETTINGS' => $outputGroup['OutputGroupSettings']['FileGroupSettings']['Destination'] = $outputBucket,
                    'HLS_GROUP_SETTINGS' => $outputGroup['OutputGroupSettings']['HlsGroupSettings']['Destination'] = $outputBucket,
                };

                return $outputGroup;
            })->all();
    }

    private function client(Account $account): MediaConvertClient
    {
        return new MediaConvertClient([
            'credentials' => [
                'key' => config('services.aws.key'),
                'secret' => config('services.aws.secret'),
            ],
            'region' => config("services.aws.media_convert.{$account->region}.region"),
            'version' => config("services.aws.media_convert.{$account->region}.version"),
            'endpoint' => config("services.aws.media_convert.{$account->region}.endpoint"),
        ]);
    }
}
