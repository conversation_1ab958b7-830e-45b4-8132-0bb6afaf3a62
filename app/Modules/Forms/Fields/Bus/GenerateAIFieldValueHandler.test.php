<?php

namespace AwardForce\Modules\Forms\Fields\Bus;

use AwardForce\Library\AIAgents\Enums\Model;
use AwardForce\Library\AIAgents\ValueObjects\Prompt;
use AwardForce\Modules\AIAgents\Boundary\AIAgent;
use AwardForce\Modules\AIAgents\Boundary\PromptContext;
use AwardForce\Modules\AIAgents\Boundary\Resource;
use AwardForce\Modules\AIAgents\Boundary\ResourceType;
use AwardForce\Modules\Entries\Enums\AIFieldContext;
use AwardForce\Modules\Entries\Enums\AIFieldTrigger;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Fields\Database\Repositories\FieldRepository;
use AwardForce\Modules\Forms\Fields\Services\ValuesService;
use Illuminate\Database\Eloquent\Casts\Json;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

final class GenerateAIFieldValueHandlerTest extends BaseTestCase
{
    use Database;
    use Laravel;

    public function testItSavesAIField(): void
    {
        $field = $this->muffin(Field::class, [
            'type' => Field::TYPE_AI,
            'configuration' => Json::encode([
                'prompt' => 'Tell me a short story about a brave knight.',
                'contexts' => [AIFieldContext::Entry],
                'triggers' => [AIFieldTrigger::EntrySubmitted],
            ]),
        ]);
        $entry = $this->muffin(Entry::class);
        $aiAgent = $this->mock(AIAgent::class);
        $aiAgent->shouldReceive('promptContext')
            ->withArgs(function (Resource $resource, array $contexts) use ($entry) {
                return $resource->type === ResourceType::Entry
                    && $resource->id === $entry->id
                    && $contexts === [AIFieldContext::Entry->value];
            })
            ->once()
            ->andReturn(new PromptContext(['processed' => 'data']));
        $aiAgent->shouldReceive('generateText')
            ->withArgs(function (Resource $resource, Prompt $prompt, array $metaData) use ($entry, $field) {
                return $resource->type === ResourceType::Entry
                    && $resource->id === $entry->id
                    && $prompt->model === Model::Claude35Sonnet
                    && $prompt->userPrompt === 'Tell me a short story about a brave knight.'
                    && $metaData === ['field_id' => $field->id];
            })
            ->once()
            ->andReturn($response = 'this is a test response');
        $aiAgent->shouldReceive('model')
            ->withArgs(function (Resource $resource) use ($entry) {
                return $resource->type === ResourceType::Entry
                    && $resource->id === $entry->id;
            })
            ->once()
            ->andReturn($entry);

        $command = new GenerateAIFieldValue(new Resource(ResourceType::Entry, $entry->id), $field->id);
        $handler = new GenerateAIFieldValueHandler(
            app(FieldRepository::class),
            $aiAgent,
            app(ValuesService::class)
        );

        $handler->handle($command);

        $entry->refresh();
        $this->assertEquals(
            [(string) $field->slug => $response],
            $entry->values
        );
    }
}
