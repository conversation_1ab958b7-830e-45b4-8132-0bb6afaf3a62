<?php

namespace AwardForce\Modules\Forms\Fields\Http\Controllers;

use AwardForce\Http\Requests\Tab\DeleteFieldsRequest;
use AwardForce\Modules\Forms\Fields\Bus\AddFieldCommand;
use AwardForce\Modules\Forms\Fields\Bus\DeleteFieldsCommand;
use AwardForce\Modules\Forms\Fields\Bus\MakeFieldCommand;
use AwardForce\Modules\Forms\Fields\Bus\UndeleteFieldsCommand;
use AwardForce\Modules\Forms\Fields\Bus\UpdateFieldCommand;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Fields\Database\Repositories\FieldRepository;
use AwardForce\Modules\Forms\Fields\Http\Requests\AddFieldRequest;
use AwardForce\Modules\Forms\Fields\Http\Requests\SelectedFieldRequest;
use AwardForce\Modules\Forms\Fields\Http\Requests\UpdateFieldRequest;
use AwardForce\Modules\Forms\Fields\View\EditField;
use AwardForce\Modules\Forms\Fields\View\FieldListView;
use AwardForce\Modules\Forms\Fields\View\NewField;
use AwardForce\Modules\Forms\Fields\View\ResourceSelector;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\View;
use Platform\Bus\Commands\CopyModelsCommand;
use Platform\Http\Controller;

class FieldController extends Controller
{
    use DispatchesJobs;

    protected $fields;

    public function __construct(FieldRepository $fields)
    {
        $this->fields = $fields;
    }

    /**
     * @return mixed
     */
    public function index(FieldListView $view)
    {
        return $this->respond('field.index', $view);
    }

    /**
     * Render the form for selection of a resource.
     *
     * @return mixed
     */
    public function getResource(ResourceSelector $view)
    {
        return $this->respond('field.resource', $view);
    }

    /**
     * Render the form for a new field.
     *
     * @return mixed
     */
    public function getNew(NewField $view)
    {
        return $this->respond('field.new', $view);
    }

    /**
     * @return \Illuminate\Http\RedirectResponse
     */
    public function add(AddFieldRequest $request)
    {
        $this->dispatch(new AddFieldCommand(
            $request->get('seasonId'),
            $request->get('formId'),
            $request->get('translated', []),
            $request->get('tabId'),
            $request->get('resource'),
            $request->get('type'),
            $request->get('seasonal'),
            $request->get('roleOption', 'all'),
            $request->get('roleAccessSettings', []),
            $request->get('categoryOption'),
            $request->get('categories', []),
            $request->get('options'),
            $request->get('order'),
            $request->get('autoScoring', 0),
            $request->boolean('autoTag'),
            $request->all()
        ));

        return overridable_redirect(route('field.index'));
    }

    /**
     * @return mixed
     */
    public function edit(EditField $view)
    {
        return $this->respond('field.edit', $view);
    }

    /**
     * @return mixed
     */
    public function update(UpdateFieldRequest $request)
    {
        $field = $request->route('field');

        $willUpdateValues = $field->updatesValues(
            (bool) $request->get('searchable'),
            (string) $request->get('protection', $field->protection)
        );

        $this->dispatch(new UpdateFieldCommand(
            $field,
            $request->get('translated', []),
            $request->get('tabId'),
            $request->get('resource'),
            $request->get('type'),
            $request->get('seasonal'),
            $request->get('roleOption', 'all'),
            $request->get('roleAccessSettings', []),
            $request->get('categoryOption'),
            $request->get('categories', []),
            $request->get('options'),
            $request->get('order'),
            $request->get('autoScoring', 0),
            $request->boolean('autoTag'),
            $request->all()
        ));

        return redirect(request()->get('redirect', route('field.index')))->with(
            $willUpdateValues ?
                ['message' => trans('fields.flash-message'), 'type' => 'info', 'messageClass' => 'hide-on-field-'.$field->slug] :
                []
        );
    }

    /**
     * @return mixed
     */
    public function preview(Request $request)
    {
        $field = $this->dispatchSync(new MakeFieldCommand(
            $request->integer('seasonId'),
            $request->integer('formId'),
            $request->get('translated', []),
            $request->integer('tabId'),
            $request->string('resource'),
            $request->string('type'),
            $request->string('roleOption', 'all'),
            $request->get('roleAccessSettings', []),
            $request->string('categoryOption', 'all'),
            [],
            $request->string('options'),
            $request->integer('order'),
            $request->boolean('autoScoring'),
            $request->boolean('autoTag'),
            $request->all()
        ));

        try {
            $renderedView = View::make('html.field.form.field', [
                'field' => $field,
                'fullWidth' => true,
                'name' => 'preview',
            ])->render();

            return response()->json([
                'success' => true,
                'data' => $renderedView,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'data' => null,
                'message' => trans('fields.preview.error'),
            ]);
        }
    }

    /**
     * Delete field(s).
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function delete(DeleteFieldsRequest $request)
    {
        $errored = $this->dispatchSync(new DeleteFieldsCommand($request->get('selected', [])));
        $redirect = overridable_redirect(route('field.index'));

        if (! $errored) {
            return $redirect;
        }
        $fieldTitles = array_map(function ($field) {
            return $field->title;
        }, $errored);

        return $redirect
            ->with('message', trans('fields.messages.half_deleted', ['field_titles' => implode(', ', $fieldTitles)]))
            ->with('type', 'warning');
    }

    /**
     * Undelete a given set of fields.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function undelete(Request $request)
    {
        $errored = $this->dispatchSync(new UndeleteFieldsCommand($request->get('selected', [])));

        if ($errored->isEmpty()) {
            return redirect()->route('field.index');
        }

        /** @var \Illuminate\Support\Collection<int, Field> */
        $fieldTitles = $errored->map(fn(Field $field) => $field->title);

        return redirect()->route('field.index')
            ->with('message', trans('fields.messages.half_restored', ['field_titles' => $fieldTitles->implode(',')]))
            ->with('type', 'warning');
    }

    /**
     * Copy Field(s).
     *
     * @Post("field/copy", as="field.copy")
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function copy(SelectedFieldRequest $request)
    {
        $this->dispatch(new CopyModelsCommand($request->get('selected', []), $this->fields));

        return redirect()->route('field.index');
    }

    public function getByForm(FieldRepository $fields, Form $form)
    {
        return response()->json(
            translate($this->fields->getContactableInForm($form->id))
                ->map(fn(Field $field) => ['slug' => (string) $field->slug, 'name' => lang($field, 'title')])
                ->all()
        );
    }
}
