<?php

namespace AwardForce\Modules\Forms\Fields\View;

use AwardForce\Modules\Audit\Data\EventLogRepository;
use AwardForce\Modules\Categories\Contracts\CategoryRepository;
use AwardForce\Modules\Entries\Contracts\EntryRepository;
use AwardForce\Modules\Forms\Fields\Database\DataAccess\Fields;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Fields\Database\Repositories\FieldRepository;
use AwardForce\Modules\Forms\Fields\Services\CompatibleTypes;
use AwardForce\Modules\Forms\Fields\Services\ConditionalFieldsListBuilder;
use AwardForce\Modules\Forms\Tabs\Database\Repositories\TabRepository;
use AwardForce\Modules\Identity\Roles\Contracts\RoleRepository;
use AwardForce\Modules\Integrations\Data\IntegrationRepository;
use AwardForce\Modules\Payments\Repositories\CurrencyRepository;
use AwardForce\Modules\Seasons\Contracts\SeasonRepository;
use Illuminate\Contracts\Config\Repository;
use Illuminate\Http\Request;
use Mockery as m;
use Tectonic\LaravelLocalisation\Translator\Engine;
use Tests\IntegratedTestCase;

final class NewFieldTest extends IntegratedTestCase
{
    private $request;
    private $config;
    private $view;
    private $fields;

    public function init()
    {
        $this->request = m::mock(Request::class);

        $this->config = m::mock(Repository::class);
        $this->fields = m::mock(FieldRepository::class);
        $this->config->shouldReceive('get')->with('awardforce.fields.resources')->andReturn(['resourceA', 'resourceB', 'resourceC'])->byDefault();
        $this->config->shouldReceive('get')->with('awardforce.tabs.resources')->andReturn(['resourceA', 'resourceB', 'resourceZ'])->byDefault();

        $this->view = new NewField(
            $this->request,
            $this->config,
            m::mock(Engine::class),
            m::mock(EventLogRepository::class),
            m::mock(CategoryRepository::class),
            $this->fields,
            m::mock(RoleRepository::class),
            m::mock(TabRepository::class),
            m::mock(SeasonRepository::class),
            m::mock(ConditionalFieldsListBuilder::class),
            m::mock(CompatibleTypes::class),
            m::mock(IntegrationRepository::class),
            m::mock(CurrencyRepository::class),
            m::mock(EntryRepository::class)
        );
    }

    public function testValidResource(): void
    {
        $this->request->shouldReceive('get')->once()->andReturn('resourceB');

        $this->assertEquals('resourceB', $this->view->resource);
    }

    public function testInvalidResource(): void
    {
        $this->request->shouldReceive('get')->once()->andReturn('invalid resource');

        $this->assertEquals('resourceA', $this->view->resource);
    }

    public function testFieldTitles(): void
    {
        $fields = $this->muffins(2, Field::class, ['resource' => Field::RESOURCE_FORMS]);
        $this->fields->shouldReceive('getAllFormulaCompatible')->once()->andReturn(new Fields($fields));

        $this->assertSame($this->view->fieldTitles, [
            (string) $fields[0]->slug => $fields[0]->title,
            (string) $fields[1]->slug => $fields[1]->title,
        ]);
    }
}
