<?php

namespace AwardForce\Modules\Forms\Fields\View;

use AwardForce\Library\Authorization\Consumer;
use AwardForce\Library\Html\VueData;
use AwardForce\Modules\Audit\Data\EventLogRepository;
use AwardForce\Modules\Categories\Composers\CategoryComposable;
use AwardForce\Modules\Categories\Contracts\CategoryRepository;
use AwardForce\Modules\Categories\Services\SelectedSeasonIsActive;
use AwardForce\Modules\Entries\Contracts\EntryRepository;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Fields\Database\Repositories\FieldRepository;
use AwardForce\Modules\Forms\Fields\FieldsResource;
use AwardForce\Modules\Forms\Fields\Services\CompatibleTypes;
use AwardForce\Modules\Forms\Fields\Services\ConditionalFieldsListBuilder;
use AwardForce\Modules\Forms\Fields\Traits\TableFieldConfigurator;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\Forms\Tabs\Database\Entities\Tab as TabModel;
use AwardForce\Modules\Forms\Tabs\Database\Repositories\TabRepository;
use AwardForce\Modules\Identity\Roles\Contracts\RoleRepository;
use AwardForce\Modules\Integrations\Data\IntegrationRepository;
use AwardForce\Modules\Payments\Repositories\CurrencyRepository;
use AwardForce\Modules\Seasons\Contracts\SeasonRepository;
use AwardForce\Modules\Seasons\Models\Season;
use Illuminate\Contracts\Config\Repository;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Platform\Html\Tabular\Tab;
use Platform\Html\Tabular\Tabular;
use Platform\View\View;
use Tectonic\LaravelLocalisation\Translator\Engine;

abstract class FieldView extends View
{
    use CategoryComposable;
    use SelectedSeasonIsActive;
    use TableFieldConfigurator;

    public function __construct(
        protected Request $request,
        protected Repository $config,
        protected Engine $translator,
        protected EventLogRepository $eventLogs,
        protected CategoryRepository $categories,
        protected FieldRepository $fields,
        protected RoleRepository $roles,
        protected TabRepository $tabs,
        protected SeasonRepository $seasons,
        protected ConditionalFieldsListBuilder $listBuilder,
        protected CompatibleTypes $compatibleTypes,
        protected IntegrationRepository $integrations,
        protected CurrencyRepository $currencies,
        protected EntryRepository $entries,
    ) {
        VueData::registerTranslations(['miscellaneous.drag_and_drop']);
    }

    abstract public function field(): Field;

    abstract public function form(): ?Form;

    abstract public function resource();

    public function hasForm(): bool
    {
        return FieldsResource::from($this->resource())->formable();
    }

    /**
     * @return Tabular
     */
    public function tabular()
    {
        $tabular = new Tabular($this->request, 'normal');
        $tabular->addTab(new Tab(trans('fields.tabs.details'), 'field.tabs.details', Tab::STATUS_ACTIVE));

        return $tabular;
    }

    /**
     * @return Season
     */
    public function season()
    {
        $season = $this->seasons->getById($this->field->seasonId);

        return $this->translator->shallow($season);
    }

    /**
     * @return array
     */
    public function categories()
    {
        return $this->hasForm() ?
            $this->formCategoriesForMultiselect($this->categories, $this->form()) :
            $this->categoriesForMultiselect($this->categories, $this->season);
    }

    /**
     * @return array
     */
    public function categoryIds()
    {
        return $this->field->categories->pluck('id')->toArray();
    }

    /**
     * @return array
     */
    public function conditionalOptions()
    {
        $conditionalOptions = [];

        foreach (config('awardforce.fields.conditionals') as $key => $value) {
            $conditionalOptions[$value] = trans("fields.form.conditional.options.{$key}");
        }

        return $conditionalOptions;
    }

    /**
     * @return Collection
     */
    public function conditionalFields()
    {
        return $this->hasForm() ?
            $this->listBuilder->getAvailableFormField($this->form()->id, $this->field->id) :
            $this->listBuilder->getAvailableField($this->season->id, $this->field->id);
    }

    /**
     * If a role is set to default, then we mark it as such.
     *
     * @return array
     */
    public function roles()
    {
        $roles = $this->translator->translate($this->roles->getAll());

        $selectableRoles = for_select($roles);

        $role = $roles->where('default', true)->first();

        if ($role) {
            $selectableRoles[$role->id] = $selectableRoles[$role->id].' (Default)';
        }

        return $selectableRoles;
    }

    /**
     * @return array
     */
    public function roleIds()
    {
        return $this->field->roles->just('id');
    }

    /**
     * @return Collection
     */
    public function roleAccessSettings()
    {
        return $this->field->roles->keyBy('id')->map(function ($role) {
            return [
                'readAccess' => $role->pivot->read_access,
                'writeAccess' => $role->pivot->write_access,
                'required' => $role->pivot->required,
            ];
        });
    }

    /**
     * @return array
     */
    public function compatibleTypes()
    {
        if ($this->field->hasValues) {
            return $this->compatibleTypes->getSortedList($this->field->type);
        }

        return sort_config_list('awardforce.fields.types', 'fields.types');
    }

    /**
     * @return string
     */
    public function preferredLanguage()
    {
        return Consumer::user()->preferredLanguage()->code;
    }

    /**
     * @return array
     */
    public function supportedLanguages()
    {
        return current_account()->supportedLanguageCodes();
    }

    public function configuration(): array
    {
        return $this->field->hasConfiguration() ? $this->fieldConfiguration->toArray() : [];
    }

    public function translatedConfiguration(): array
    {
        return $this->fieldConfiguration->translated() ? $this->fieldConfiguration->translated() : [];
    }

    /**
     * @return \AwardForce\Modules\Forms\Fields\Configurations\Configuration
     */
    public function fieldConfiguration()
    {
        return $this->translator->translate($this->field->getConfiguration());
    }

    public function oldTranslatedConfiguration(): array
    {
        return old_translatable('translatedConfiguration');
    }

    public function plagiarismDetection()
    {
        return $this->integrations->activePlagiarismDetection($this->field->seasonId);
    }

    public function oldTranslated()
    {
        return old_translatable('translated');
    }

    public function resourceTabs()
    {
        if ($this->resource === Field::RESOURCE_USERS) {
            return [];
        }

        if ($this->resource === Field::RESOURCE_FORMS) {
            $types = [TabModel::TYPE_DETAILS, TabModel::TYPE_FIELDS, TabModel::TYPE_ATTACHMENTS, TabModel::TYPE_CONTRIBUTORS, TabModel::TYPE_ELIGIBILITY, TabModel::TYPE_REFEREES];
        }

        return for_select(translate($this->tabs->forResourceTypeAndForm(Field::RESOURCE_FORMS, $types ?? [$this->resource], $this->form->id)));
    }

    public function translatedField()
    {
        if (! $this->field->id) {
            $this->field->injectTranslations(old('translated'));
            $this->field->options = old('options');
            $this->field->autoScoring = (int) old('autoScoring');
        }

        $field = $this->field->toArray();
        $field['options'] = $this->field->options->forList();
        $field['translated'] = $this->field->translated;

        return $field;
    }

    public function draggableOptionsTranslations()
    {
        return translations_for_vue(\Consumer::languageCode(), [
            'fields.form.options',
            'fields.form.label.label',
            'fields.alerts',
            'fields.auto_score',
            'fields.auto_tag',
            'buttons.delete',
            'buttons.cancel',
            'buttons.on',
            'buttons.off',
            'validation.required',
            'validation.required_generic',
            'fields.form.formula',
        ]);
    }

    public function currencies()
    {
        return $this->currencies->getAll()
            ->map(function ($currency) {
                $currency->name = $currency->name();

                return $currency;
            });
    }

    public function readOnly(): bool
    {
        return ! $this->canModify();
    }

    public function fieldTitles(): array
    {
        return translate($this->fields->getAllFormulaCompatible())?->pluck('title', 'slug')->toArray();
    }

    public function hasEntries(): bool
    {
        if ($this->field->type !== Field::TYPE_TABLE) {
            return false;
        }

        return $this->entries->existsForField($this->field);
    }

    public function showConditionalSettings(): bool
    {
        return ! in_array($this->resource, [
            Field::RESOURCE_USERS,
            //            Field::RESOURCE_ORGANISATIONS
        ]);
    }

    public function showEntrantAccessSettings(): bool
    {
        return ! in_array($this->resource, [
            Field::RESOURCE_USERS,
            //            Field::RESOURCE_ORGANISATIONS
        ]);
    }
}
