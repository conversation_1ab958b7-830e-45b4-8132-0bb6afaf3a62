<?php

namespace AwardForce\Modules\Forms\Fields\View\Compose;

use AwardForce\Library\Authorization\UserConsumer;
use AwardForce\Modules\Accounts\Models\Membership;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Identity\Users\Models\User;
use Illuminate\Support\ViewErrorBag;
use Mockery as m;
use Tests\IntegratedTestCase;

abstract class OptionableRendering extends IntegratedTestCase
{
    abstract protected function template(): string;

    public function testRenderView(): void
    {
        $field = $this->muffin(Field::class, ['options' => "Option 1\r\nOption 2\r\nOption 3"]);

        $field->addTranslation('en_GB', 'optionText', json_encode(['Option 1' => 'OPT 1', 'Option 2' => 'OPT 2', 'Option 3' => 'OPT 3']));
        $field->addTranslation('el_GR', 'optionText', json_encode(['Option 1' => 'GREEK OPT 1', 'Option 2' => null, 'Option 3' => 'GREEK OPT 3']));
        $user = $this->muffin(User::class);
        $this->muffin(Membership::class, ['user_id' => $user->id, 'account_id' => $this->account->id, 'language' => 'el_GR']);
        \Consumer::set(new UserConsumer($user));

        $view = view($this->template(), compact('field'));
        $errors = view('partials.errors.inline');
        $factory = m::spy(\Illuminate\Contracts\View\Factory::class)->makePartial();
        $factory->shouldReceive('getShared')->andReturn(['errors' => new ViewErrorBag]);
        $factory->shouldReceive('make')->with('partials.errors.inline', m::any())->andReturn($errors);
        app()->instance(\Illuminate\Contracts\View\Factory::class, $factory);
        $factory->shouldReceive('getShared')->andReturn(['errors' => new ViewErrorBag]);
        $factory->shouldReceive('make')->andReturn($view);

        $html = $view->render();

        $this->assertStringContainsString('GREEK OPT 1', $html);
        // Fallback
        $this->assertStringContainsString('OPT 2', $html);
        $this->assertStringContainsString('GREEK OPT 3', $html);
        $this->assertStringNotContainsString('NTA', $html);
    }
}
