<?php

namespace AwardForce\Modules\Forms\Fields\View;

use AwardForce\Modules\Files\Contracts\FileRepository;
use AwardForce\Modules\Files\Models\File;
use AwardForce\Modules\Files\Services\Uploadable;
use AwardForce\Modules\Files\Services\Uploader;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Fields\Services\UploadValidator;
use AwardForce\Modules\Forms\Forms\Services\FormFileMapper;
use AwardForce\Modules\Identity\Users\Models\User;
use Facades\Platform\Strings\Output;
use Illuminate\Support\Collection;

trait VueFields
{
    use Uploadable;

    protected function mapForVue(Collection $fields, ?User $user): Collection
    {
        $files = $this->filesFromFields($fields); // Grab all to avoid n+1 queries

        return $fields->map(function (Field $field) use ($files, $user) {
            $data = [];

            $data = $this->mapFileField($data, $field, $files, $user);

            return $data + [
                'slug' => $slug = (string) $field->slug,
                'label' => (markdown_inline($field->label)),
                'labelMarkdown' => Output::html($field->label),
                'title' => $field->title,
                'value' => $field->value !== null ? $field->value : old("values.$slug"),
                'name' => "values[$slug]",
                'type' => $field->type,
                'autocomplete' => $field->autocomplete,
                'options' => $this->options($field),
                'resource' => $field->resource,
                'required' => $field->required,
                'helpText' => $field->helpText,
                'hintText' => $field->hasHintText() ? Output::html($field->hintText) : null,
                'id' => $field->id,
                'includeTimezone' => $field->includeTimezone,
                'maximumCharacters' => $field->maximumCharacters,
                'maximumWords' => $field->maximumWords,
                'minimumCharacters' => $field->minimumCharacters,
                'minimumWords' => $field->minimumWords,
                'minVideoLength' => $field->minVideoLength,
                'maxVideoLength' => $field->maxVideoLength,
                'imageDimensionConstraints' => (object) $field->imageDimensionConstraints->toArray(),
                'writeAccess' => $field->writeAccess,
            ];
        })->values();
    }

    protected function uploaderOptions(Field $field, ?User $user): string
    {
        return obfuscate($this->uploader($field, $user)->options());
    }

    protected function uploader(Field $field, ?User $user): Uploader
    {
        return app(UploadValidator::class)
            ->setField($field)
            ->setupUploader($this->setupUploader()->setForeign($user?->id))
            ->setUseFileToken();
    }

    /**
     * @param  Collection<Field>  $fields
     * @return Collection<File>
     */
    private function filesFromFields(Collection $fields): Collection
    {
        return app(FileRepository::class)
            ->getFromFileFields($fields)
            ->map(fn(File $file) => app(FormFileMapper::class)->mapFile($file));
    }

    private function mapFileField(array $data, Field $field, Collection $files, ?User $user): array
    {
        if (! $field->isFileField() || ! $user?->id) {
            return $data;
        }

        if (! empty($file = $files[$field->id] ?? null)) {
            $data['file'] = json_encode($file);
        }

        if (! isset($data['uploaderOptions'])) {
            $data['uploaderOptions'] = $this->uploaderOptions($field, $user);
        }

        return $data;
    }

    private function options(Field $field): ?array
    {
        return match ($field->type) {
            'drop-down-list' => $field->options?->forListWithEmptyOption(),
            default => $field->options?->forList()
        };
    }
}
