<?php

namespace AwardForce\Modules\Audit\Data;

use AwardForce\Library\Database\ElasticSearch\Model;
use AwardForce\Library\Values\IPAddress;
use AwardForce\Modules\Accounts\Models\Account;
use AwardForce\Modules\Audit\Events\SystemResource;
use AwardForce\Modules\Identity\Users\Models\User;
use Facades\Platform\Strings\Output;
use Illuminate\Support\Collection;

/**
 * AwardForce\Modules\Audit\Data\EventLog
 *
 * @property int $id
 * @property int $oldId
 * @property int|null $accountId
 * @property int|null $userId
 * @property int|null $jediId
 * @property int|null $seasonId
 * @property int|null $foreignId
 * @property string|null $ip
 * @property SystemResource $resource
 * @property string $action
 * @property string $description
 * @property array $data
 * @property string|null $slug
 * @property string $createdAt
 * @property-read \AwardForce\Modules\Accounts\Models\Account|null $account
 * @property-read \AwardForce\Modules\Identity\Users\Models\User|null $user
 *
 * @method static \Illuminate\Database\Eloquent\Builder|\AwardForce\Modules\Audit\Data\EventLog whereAccountId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\AwardForce\Modules\Audit\Data\EventLog whereAction($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\AwardForce\Modules\Audit\Data\EventLog whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\AwardForce\Modules\Audit\Data\EventLog whereData($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\AwardForce\Modules\Audit\Data\EventLog whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\AwardForce\Modules\Audit\Data\EventLog whereForeignId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\AwardForce\Modules\Audit\Data\EventLog whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\AwardForce\Modules\Audit\Data\EventLog whereIp($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\AwardForce\Modules\Audit\Data\EventLog whereJediId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\AwardForce\Modules\Audit\Data\EventLog whereResource($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\AwardForce\Modules\Audit\Data\EventLog whereSeasonId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\AwardForce\Modules\Audit\Data\EventLog whereUserId($value)
 *
 * @mixin \Eloquent
 */
class EventLog extends Model
{
    protected $type = 'event_logs';
    protected $index = 'event_logs_01';
    protected $primaryKey = '_id';
    public $timestamps = false;
    protected $fillable = ['resource', 'action', 'description', 'data', 'slug', 'ip', 'created_at', 'user_id', 'jedi_id', 'account_id', 'old_id'];

    public function account()
    {
        return $this->belongsTo(Account::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Creates a new audit log object and saves it to the database.
     *
     * @param  Account|null  $account
     * @param  int  $userId
     * @param  int|null  $jediId
     * @param  null  $createdAt
     * @param  int  $oldId
     * @return EventLog
     *
     * @internal param int $seasonId
     */
    public static function log($account, $userId, $jediId, string $ip, SystemResource $resource, string $action, string $description, ?array $data = null, int $foreignId = 0, ?string $slug = null, $createdAt = null, $oldId = 0)
    {
        $audit = new EventLog(compact('resource', 'action', 'description', 'data', 'slug', 'ip'));
        $audit->setAccount($account);
        $audit->setSeason($account);
        $audit->userId = $userId;
        $audit->jediId = (int) $jediId;
        $audit->foreignId = $foreignId;
        $audit->createdAt = $createdAt ?: $audit->freshTimestamp()->toDateTimeString();
        $audit->oldId = $oldId;

        $audit->save();

        return $audit;
    }

    /**
     * Retrieves the account id, if one is to be found.
     *
     * @param  Account|null  $account
     */
    private function setAccount($account)
    {
        $this->accountId = $account ? $account->id : null;
    }

    /**
     * Sets the season id based on the account, if available.
     *
     * @param  Account|null  $account
     */
    private function setSeason($account)
    {
        $this->seasonId = $account && $account->activeSeason() ? $account->activeSeason()->id : null;
    }

    /**
     * The resource attribute when assigned needs to be pulled from the system resource object.
     */
    public function setResourceAttribute(SystemResource|string $resource)
    {
        $this->attributes['resource'] = (string) $resource;
    }

    /**
     * Ensures that the system resource is returned as the required value object.
     *
     * @return SystemResource
     */
    public function getResourceAttribute($resource)
    {
        return new SystemResource($resource);
    }

    public function getIpAttribute($value): IPAddress
    {
        return IPAddress::parse($value);
    }

    /**
     * Strips non-scalar values (numeric, string) from data to use as in the description string replacements.
     */
    public function descriptionData(): array
    {
        return collect($this->data)
            ->filter(fn($value) => is_scalar($value))
            ->map(fn($value) => Output::text($value))
            /**
             * Some event logs were created with the slug as an empty array due to improper serialization.
             * This merges the slug value if it is not already present in the data, ensuring the log description can be generated correctly.
             */
            ->tap(fn(Collection $collection) => $collection->when(
                ! $collection->has('slug') && $this->slug,
                fn(Collection $collection) => $collection->put('slug', $this->slug)
            ))
            ->toArray();
    }

    public function getActionAttribute($action)
    {
        $segments = explode('.', $action);

        return array_pop($segments);
    }

    public function generatedBy(): string
    {
        switch (true) {
            case ! $this->user:
                return 'system';
            case (explode('.', $this->attributes['action'])[0] ?? 'user') === 'api':
                return 'api';
            default:
                return 'user';
        }
    }
}
