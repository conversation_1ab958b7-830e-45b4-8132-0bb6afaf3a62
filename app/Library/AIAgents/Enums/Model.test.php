<?php

namespace AwardForce\Library\AIAgents\Enums;

use RuntimeException;
use Tests\BaseTestCase;
use Tests\Concerns\Laravel;

final class ModelTest extends BaseTestCase
{
    use Laravel;

    public function testModelProvider(): void
    {
        $this->assertSame(Provider::Bedrock, Model::Claude37Sonnet->provider());
    }

    public function testModelIdIsCorrect(): void
    {
        $this->assertSame('anthropic.claude-3-5-sonnet-20241022-v2:0', Model::Claude35SonnetV2->modelId('au'));
        $this->assertSame('us.anthropic.claude-3-5-sonnet-20241022-v2:0', Model::Claude35SonnetV2->modelId('us'));
    }

    public function testModelIdThrowAnErrorForUnavailableRegions(): void
    {
        $model = Model::Claude35SonnetV2;

        $this->expectException(RuntimeException::class);
        $this->expectExceptionMessage("Model {$model->name} is not available in ca region");

        $model->modelId('ca');
    }

    public function testCrossRegionIsSubsetOfRegions(): void
    {
        foreach (Model::cases() as $model) {
            $metadata = $model->metadata();

            foreach (array_get($metadata, 'crossRegions') as $crossRegion) {
                $this->assertContains(
                    $crossRegion,
                    array_get($metadata, 'supportedRegions'),
                    "Model {$model->name}: {$crossRegion} is not listed in supported regions to be able to allow cross-region inference."
                );
            }
        }
    }

    public function testCasesForRegion(): void
    {
        $this->assertEqualsCanonicalizing([
            Model::Claude35SonnetV2,
            Model::MistralLarge,
        ], Model::casesFor('au'));

        $this->assertEqualsCanonicalizing([
            Model::MistralLarge,
        ], Model::casesFor('ca'));

        $this->assertEquals([], Model::casesFor('non_existent_region'));
    }

    public function testIsAvailableInRegion(): void
    {
        $this->assertTrue(Model::Claude35Sonnet->availableIn('us'));
        $this->assertTrue(Model::Claude35Sonnet->availableIn('eu'));
        $this->assertFalse(Model::Claude35Sonnet->availableIn('au'));
        $this->assertFalse(Model::Claude35Sonnet->availableIn('ca'));

        $this->assertTrue(Model::MistralLarge->availableIn('us'));
        $this->assertTrue(Model::MistralLarge->availableIn('eu'));
        $this->assertTrue(Model::MistralLarge->availableIn('au'));
        $this->assertTrue(Model::MistralLarge->availableIn('ca'));
        $this->assertFalse(Model::MistralLarge->availableIn('non_existent_region'));
    }
}
