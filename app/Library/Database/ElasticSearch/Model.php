<?php

namespace AwardForce\Library\Database\ElasticSearch;

use Elasticsearch\Endpoints\Indices\Refresh;
use Elasticsearch\Namespaces\BooleanRequestWrapper;

class Model extends \AwardForce\Library\Database\Eloquent\Model
{
    use BooleanRequestWrapper;

    /**
     * Create a new Elasticsearch model instance.
     *
     * @param  array  $attributes
     * @param  bool  $exists
     */
    public function __construct($attributes = [], $exists = false)
    {
        $this->connection = $this->getConnection();
        parent::__construct($attributes);

        $this->exists = $exists;
    }

    /**
     * Get current connection
     *
     * @return string
     */
    public function getConnection()
    {
        return $this->connection ?: config('es.default');
    }

    /**
     * Set current connection
     *
     * @return void
     */
    public function setConnection($connection)
    {
        $this->connection = $connection;
    }

    /**
     * Get index name
     *
     * @return string
     */
    public function getIndex()
    {
        return $this->index;
    }

    /**
     * Set index name
     *
     * @return void
     */
    public function setIndex($index)
    {
        $this->index = $index;
    }

    /**
     * Get type name
     *
     * @return string
     */
    public function getType()
    {
        return $this->type;
    }

    /**
     * Set type name
     *
     * @return void
     */
    public function setType($type)
    {
        $this->type = $type;
    }

    /**
     * Create a new model query
     *
     * @return mixed
     */
    public function newQuery()
    {
        $query = app('es')->setModel($this);

        $query->connection($this->getConnection());

        if ($index = $this->getIndex()) {
            $query->index($index);
        }

        if ($type = $this->getType()) {
            $query->type($type);
        }

        return $query;
    }

    /**
     * Get all model records
     *
     * @return mixed
     */
    public static function all($columns = ['*'])
    {
        $instance = new static;

        $model = $instance->newQuery()->get($columns);

        return $model;
    }

    /**
     * Get model by key
     *
     *
     * @return mixed
     */
    public static function find($key)
    {
        $instance = new static;

        $model = $instance->newQuery()->id($key)->take(1)->first();

        if ($model) {
            $model->exists = true;
        }

        return $model;
    }

    /**
     * Delete model record
     *
     * @return \Basemkhirat\Elasticsearch\Model|bool
     */
    public function delete()
    {
        if (! $this->exists()) {
            return false;
        }

        $this->newQuery()->id($this->getID())->delete();

        $this->exists = false;

        return $this;
    }

    /**
     * Save data to model
     *
     * @return string
     */
    public function save(array $options = [])
    {
        $fields = array_except(
            $this->attributes,
            ['_index', '_type', '_id', '_score']
        );

        if ($this->exists()) {
            // Update the current document

            $this->newQuery()->id($this->getID())->update($fields);
        } else {
            // Check if model key exists in items

            if (array_key_exists('_id', $this->attributes)) {
                $created = $this->newQuery()->id($this->attributes['_id'])
                    ->insert($fields);
                $this->_id = $this->attributes['_id'];
            } else {
                $created = $this->newQuery()->insert($fields);
                $this->_id = $created->_id;
            }

            $this->setConnection($this->getConnection());
            $this->setIndex($created->_index);

            // Match earlier versions

            $this->_index = $created->_index;
            $this->_type = $this->type;

            $this->exists = true;
        }

        return $this;
    }

    /**
     * Check model is exists
     *
     * @return bool
     */
    public function exists()
    {
        return $this->exists;
    }

    /**
     * Get model key
     *
     * @return mixed
     */
    public function getID()
    {
        return $this->attributes['_id'];
    }

    /**
     * Handle dynamic static method calls into the method.
     *
     * @param  string  $method
     * @param  array  $parameters
     * @return mixed
     */
    public static function __callStatic($method, $parameters)
    {
        return (new static)->$method(...$parameters);
    }

    /**
     * Handle dynamic method calls into the model.
     *
     * @param  string  $method
     * @param  array  $parameters
     * @return mixed
     */
    public function __call($method, $parameters)
    {
        return $this->newQuery()->$method(...$parameters);
    }

    /**
     * @return string
     */
    public function getDateFormat()
    {
        return 'Y-m-d H:i:s';
    }

    protected function newRelatedInstance($class)
    {
        return new $class;
    }

    public static function refreshIndex()
    {
        try {
            self::performRequest(new Refresh(), self::raw()->transport);
        } catch (\Throwable $e) {
        }
    }

    public function defaultRawQueryParameters()
    {
        return [
            'index' => $this->getIndex(),
            'type' => $this->getType(),
        ];
    }
}
