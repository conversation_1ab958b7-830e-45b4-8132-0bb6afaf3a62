<?php

namespace AwardForce\Library\Providers;

use Illuminate\Support\Facades\Route;

class ModuleServiceProvider extends \Platform\Providers\ModuleServiceProvider
{
    protected function registerApiRoutes(string $path): void
    {
        Route::middleware('api')
            ->group($path);
    }

    protected function registerKesselRoutes(string $path): void
    {
        Route::kessel()
            ->group($path);
    }

    protected function registerWebRoutes(string $path): void
    {
        Route::web()
            ->group($path);
    }

    protected function registerConfigurationRoutes(string $path): void
    {
        $this->registerWebRoutes($path);
    }
}
