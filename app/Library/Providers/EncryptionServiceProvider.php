<?php

namespace AwardForce\Library\Providers;

use AwardForce\Application;
use Aws\Kms\KmsClient;
use Illuminate\Support\ServiceProvider;
use Platform\Encryption\Encrypter;
use Platform\Encryption\ParanoidEncrypter;

class EncryptionServiceProvider extends ServiceProvider
{
    /** @var bool */
    public $defer = true;

    public function boot()
    {
        $this->app->singleton(Encrypter::class, function (Application $app) {
            $encrypter = $app->make('encrypter');
            $cipher = $app['config']->get('app.cipher');

            if ($app->isLocal() || $app->runningUnitTests()) {
                return new Encrypter($encrypter, $encrypter);
            }

            // Note: paranoid<PERSON><PERSON> passes in a callback to be executed on demand
            return new Encrypter($encrypter, new ParanoidEncrypter($this->paranoidKey($app), $cipher));
        });
    }

    /**
     * Get the services provided by the provider.
     *
     * @return array
     */
    public function provides()
    {
        return [Encrypter::class];
    }

    /**
     * Builds the callback that will be executed when the paranoid encrypter is used in a request.
     * This prevents the round-trip to KMS from affecting all page requests if not needed.
     * Currently it's only used for field values set to maximum protection.
     */
    private function paranoidKey(Application $app): callable
    {
        return function () use ($app) {
            $config = $app['config']->get('services.aws');

            $args = [
                'credentials' => [
                    'key' => $config['key'],
                    'secret' => $config['secret'],
                ],
                'region' => $config['kms']['region'],
                'version' => '2014-11-01',
            ];

            return (new KmsClient($args))
                ->decrypt(['CiphertextBlob' => base64_decode($config['kms']['maximum'])])
                ->get('Plaintext');
        };
    }
}
