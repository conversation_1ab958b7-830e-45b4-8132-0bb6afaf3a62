name: Amplitude PHP CI

on:
  push:
    branches: [ master ]
  pull_request:
    branches: [ master ]

jobs:
  testsuite:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        php-version: ['7.2', '7.3', '7.4']
    name: PHP ${{ matrix.php-version }}
    steps:
    - uses: actions/checkout@v2
    - name: Setup PHP with XDebug
      uses: shivammathur/setup-php@v2
      with:
        php-version: ${{ matrix.php-version }}
        coverage: xdebug
    - name: Validate composer.json
      run: composer validate
    - name: Install dependencies
      run: composer install
    - name: Run test suite
      run: vendor/bin/phpunit --coverage-clover build/logs/clover.xml
    - name: PSR2 coverage
      run: vendor/bin/phpcs --standard=psr2 src test

    - name: Upload coverage results to Coveralls (only for one result)
      if: matrix.php-version == '7.2'
      env:
        COVERALLS_REPO_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      run: vendor/bin/php-coveralls --coverage_clover=build/logs/clover.xml -v
