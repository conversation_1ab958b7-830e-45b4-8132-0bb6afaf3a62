import { expect } from 'chai';
import FieldLabel from '@/lib/components/Fields/FieldLabel.vue';
import { shallowMount } from '@vue/test-utils';
import { useValidationStore } from '@/../tests/utils/test-helper';

const lang = { get: () => ' (optional)' };
const getField = (required, type, label, entrantReadAccess, entrantWriteAccess) => ({
	required: required,
	type: type,
	labelMarkdown: label,
	entrantReadAccess: entrantReadAccess,
	entrantWriteAccess: entrantWriteAccess,
});

describe('FieldLabel', () => {
	it('should display optional text', async () => {
		const fieldLabel = shallowMount(FieldLabel, {
			provide: { lang },
			propsData: {
				field: {},
				isManager: false,
			},
			...useValidationStore,
		});

		await fieldLabel.setProps({
			field: getField(false, 'text', 'Label', true, true),
		});
		expect(fieldLabel.vm.isOptional).to.be.true;
		expect(fieldLabel.vm.label).to.be.equal('Label (optional)');

		await fieldLabel.setProps({
			field: getField(false, 'text', 'Multiline<br>Label', true, true),
		});
		expect(fieldLabel.vm.isOptional).to.be.true;
		expect(fieldLabel.vm.label).to.be.equal('Multiline (optional)<br>Label');

		await fieldLabel.setProps({
			field: getField(false, 'content', 'Label', true, true),
		});
		expect(fieldLabel.vm.isOptional).to.be.false;
		expect(fieldLabel.vm.label).to.be.equal('Label');

		await fieldLabel.setProps({
			field: getField(false, 'textarea', 'Multiline<br>Label', true, false),
		});
		expect(fieldLabel.vm.isOptional).to.be.false;
		expect(fieldLabel.vm.label).to.be.equal('Multiline<br>Label');
	});

	it('should handle HTML content correctly', async () => {
		const fieldLabel = shallowMount(FieldLabel, {
			provide: { lang },
			propsData: {
				field: {},
				isManager: false,
			},
			...useValidationStore,
		});

		// Set the field property with HTML content using setProps
		await fieldLabel.setProps({
			field: getField(false, 'text', 'Hello <b>world</b>!\nSecond line.', true, true),
		});

		// Access the label method correctly after ensuring it exists
		expect(fieldLabel.vm.label).to.be.equal('Hello <b>world</b>! (optional)\nSecond line.');
	});

	it('adds optional to the end of the first <p> tag label', () => {
		const fieldLabel = shallowMount(FieldLabel, {
			provide: { lang },
			propsData: {
				field: getField(false, 'text', '<p>Label</p><p>New line</p>', true, true),
				isManager: false,
			},
			...useValidationStore,
		});

		expect(fieldLabel.vm.label).to.be.equal('<p>Label (optional)</p><p>New line</p>');
	});

	it('should add optional to the end of the first li item', () => {
		const fieldLabel = shallowMount(FieldLabel, {
			provide: { lang },
			propsData: {
				field: getField(false, 'text', '<ul><li>List one</li><li>List two</li></ul>', true, true),
				isManager: false,
			},
			...useValidationStore,
		});

		expect(fieldLabel.vm.label).to.be.equal('<ul><li>List one (optional)</li><li>List two</li></ul>');
	});

	it('should add optional to the end of the first br tag', () => {
		const paragraphLabel = '<p><strong>First Line</strong><br> &nbsp;<br>Second Line</p>';

		const fieldLabel = shallowMount(FieldLabel, {
			provide: { lang },
			propsData: {
				field: getField(false, 'text', paragraphLabel, true, true),
				isManager: false,
			},
			...useValidationStore,
		});

		expect(fieldLabel.vm.label).to.be.equal('<p><strong>First Line</strong> (optional)<br> &nbsp;<br>Second Line</p>');
	});
});
