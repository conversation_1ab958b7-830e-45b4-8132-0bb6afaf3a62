var assert = require('chai').assert;
var sinon = require('sinon');
var beforeChange = require('@/lib/simplemde-before-change.js');
var editor = {};
var updateFn = sinon.spy();
var changeObj = { from: 1, to: 2, update: updateFn };

describe('beforeChange event handler', () => {
	it('should do nothing if the selection does not contain merge fields', () => {
		editor.getSelection = sinon.stub().returns('markdown content');
		changeObj.text = ['*markdown content*'];

		beforeChange(editor, changeObj);

		assert.isFalse(updateFn.called);
	});

	it('should preserve merge fields when updating content', () => {
		editor.getSelection = sinon.stub().returns('markdown content {account_name} {entry_local_id}');
		editor.getCursor = sinon.stub().returns({ line: 1, ch: 1 });
		editor.setSelection = sinon.stub();
		changeObj.text = ['*markdown content {accountname} {entrylocalid}*'];

		before<PERSON><PERSON><PERSON>(editor, changeObj);

		assert.isTrue(updateFn.calledOnce);
		assert.equal('*markdown content {account_name} {entry_local_id}*', updateFn.getCall(0).args[2]);
	});
});
