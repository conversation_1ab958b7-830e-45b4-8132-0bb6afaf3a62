<template>
	<filtertron-tray-section id="field-configurator-text" :title="lang.get('fields.configuration.text.label')">
		<div class="fields island">
			<div class="form-group">
				<label>
					{{ lang.get('fields.form.label.label') }}
					<help-icon :content="lang.get('fields.form.label.help')" />
				</label>
				<multilingual
					mode="markdown"
					:supported-languages="supportedLanguages"
					:resource="field"
					property="label"
					@input="handleLabelInput"
				/>
			</div>
			<div class="form-group">
				<label>
					{{ lang.get('fields.form.title.label') }}
					<help-icon :content="lang.get('fields.form.title.help')" />
				</label>
				<multilingual
					mode="text"
					:supported-languages="supportedLanguages"
					:resource="field"
					property="title"
					:max-length="32"
					@input="handleTitleInput"
				/>
			</div>
			<div v-if="hasHintText" class="form-group">
				<label>
					{{ lang.get('fields.form.hint_text.label') }}
					<help-icon :content="lang.get('fields.form.hint_text.help')" />
				</label>
				<multilingual
					mode="textarea"
					:supported-languages="supportedLanguages"
					:resource="field"
					property="hintText"
					@input="(translated) => handleTranslatedInput('hintText', translated, true)"
				/>
			</div>
			<div v-if="field.type !== 'content'" class="form-group">
				<label>
					{{ lang.get('fields.form.help_text.label') }}
					<help-icon :content="lang.get('fields.form.help_text.help')" />
				</label>
				<multilingual
					mode="textarea"
					:supported-languages="supportedLanguages"
					:resource="field"
					property="helpText"
					@input="(translated) => handleTranslatedInput('helpText', translated)"
				/>
			</div>
		</div>
	</filtertron-tray-section>
</template>

<script>
import { sanitise } from '@/domain/utils/DataSanitiser';
import FiltertronTraySection from '@/lib/components/Filtertron/FiltertronTraySection';
import HelpIcon from '@/lib/components/Shared/HelpIcon';
import Multilingual from '@/lib/components/Translations/Multilingual';
import handleInputMixin from '../handle-input-mixin.js';
import handleTranslatedInputMixin from '../handle-translated-input-mixin.js';

export default {
	inject: ['lang'],
	components: {
		FiltertronTraySection,
		HelpIcon,
		Multilingual,
	},
	mixins: [handleInputMixin, handleTranslatedInputMixin],
	props: {
		field: {
			type: Object,
			required: true,
		},
	},
	data() {
		return {
			titleModified: {},
		};
	},
	computed: {
		hasHintText() {
			return this.field.type !== 'content' && this.field.resource !== 'Attachments';
		},
	},
	methods: {
		handleLabelInput(translated, language) {
			const rawLabel = (translated[this.lang.locale] || {})['label'] || '';
			const label = sanitise(this.marked(rawLabel));

			const payload = {
				translated: translated,
				label: label,
				labelMarkdown: label,
			};

			// Prefill the title
			if (!this.field.createdAt && !this.titleModified[language]) {
				payload.title = rawLabel.slice(0, 32);
				payload.translated[language].title = payload.translated[language].label.slice(0, 32);
			}

			this.$emit('input', payload);
		},
		handleTitleInput(translated, language, property, value) {
			this.titleModified[language] = !!value;
			this.handleTranslatedInput('title', translated);
		},
	},
};
</script>
