<template>
	<div>
		<div class="form-group">
			<label for="parent-selector">
				{{ lang.get('category.form.parent.label') }}
			</label>
			<select id="parent-selector" name="parent" class="form-control" :disabled="readOnly" @change="changeParent">
				<option :value="null">-</option>
				<option
					v-for="parent in parents"
					:key="'parent-' + parent.id"
					:value="parent.slug"
					:selected="isParentSelected(parent.id)"
				>
					{{ parent.name }}
				</option>
			</select>
		</div>
		<div class="form-group">
			<label>
				{{ lang.get('category.form.name.label') }}
			</label>
			<multilingual
				:supported-languages="supportedLanguages"
				:resource="selectedCategory"
				property="name"
				:disabled="readOnly"
				toolbar-media-embed
				@input="(translated) => handleTranslatedInput('name', translated) && onTranslatedInputChange(translated)"
			/>
		</div>
		<div class="form-group">
			<label>
				{{ lang.get('category.form.description.label') }}
			</label>
			<multilingual
				mode="markdown"
				:supported-languages="supportedLanguages"
				:resource="selectedCategory"
				:disabled="readOnly"
				markdown-mode="full"
				toolbar-media-embed
				property="description"
				@input="
					(translated) => handleTranslatedInput('description', translated, true) && onTranslatedInputChange(translated)
				"
			/>
		</div>
		<div class="form-group">
			<label>
				{{ lang.get('category.form.shortcode.label') }}
				<help-icon :content="lang.get('category.form.shortcode.help')" />
			</label>
			<multilingual
				mode="text"
				:supported-languages="supportedLanguages"
				:resource="selectedCategory"
				:disabled="readOnly"
				property="shortcode"
				@input="
					(translated) => handleTranslatedInput('shortcode', translated, false, false) && onTranslatedInputChange(translated)
				"
			/>
		</div>
		<div class="form-group">
			<div class="checkbox styled">
				<input type="hidden" :disabled="readOnly" name="status" value="0" />
				<input
					id="active"
					name="status"
					value="1"
					type="checkbox"
					:checked="categoryIsActive"
					:disabled="readOnly"
					@change="(e) => handleInput('active', e.target.checked)"
				/>
				<label for="active">
					{{ lang.get('category.form.active.label') }}
				</label>
			</div>
		</div>
		<div class="form-group">
			{{ lang.get('category.form.promote.label') }}

			<div class="radio styled">
				<input
					id="promoted-no"
					v-model="selectedCategory.promoted"
					type="radio"
					name="promoted"
					value="0"
					:disabled="readOnly"
					@input="onPromotedChange"
				/>
				<label for="promoted-no">
					{{ lang.get('category.form.promote.toggles.no') }}
				</label>
			</div>
			<div v-if="selectedCategory.parentId" class="radio styled">
				<input
					id="promoted-inherit"
					v-model="selectedCategory.promoted"
					type="radio"
					name="promoted"
					:value="null"
					:disabled="readOnly"
					@input="onPromotedChange"
				/>
				<label for="promoted-inherit">
					{{ lang.get('category.form.promote.toggles.inherit') }}
				</label>
			</div>
			<div class="radio styled">
				<input
					id="promoted-yes"
					v-model="selectedCategory.promoted"
					type="radio"
					name="promoted"
					value="1"
					:disabled="readOnly"
					@input="onPromotedChange"
				/>
				<label for="promoted-yes">
					{{ lang.get('category.form.promote.toggles.yes') }}
				</label>
			</div>
		</div>
	</div>
</template>

<script lang="ts">
import { categoryConfiguratorGeneralController } from '@/modules/entry-form/Configuration/CategoryConfigurator/CategoryConfiguratorGeneral.controller';
import { defineComponent } from 'vue';
import handleInputMixin from '../handle-input-mixin.js';
import handleTranslatedInputMixin from '../handle-translated-input-mixin.js';
import HelpIcon from '@/lib/components/Shared/HelpIcon';
import Multilingual from '@/lib/components/Translations/Multilingual';
import RadioGroup from '@/lib/components/Shared/RadioGroup.vue';
import toIntMixin from '../to-int-mixin.js';

export default defineComponent({
	inject: ['lang'],
	components: {
		RadioGroup,
		HelpIcon,
		Multilingual,
	},
	mixins: [handleInputMixin, handleTranslatedInputMixin, toIntMixin],
	props: {
		category: {
			type: Object,
			required: true,
		},
		categories: {
			type: Array,
			default: () => [],
		},
		readOnly: {
			type: Boolean,
			default: false,
		},
	},
	setup(props, { emit }) {
		return categoryConfiguratorGeneralController(props, emit);
	},
});
</script>
