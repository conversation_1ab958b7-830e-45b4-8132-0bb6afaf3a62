<template>
	<div>
		<div class="checkbox styled">
			<input type="hidden" name="packingSlip" value="0" />
			<input
				id="packingSlip"
				name="packingSlip"
				type="checkbox"
				value="1"
				:checked="isPackingSlipChecked"
				:disabled="isPackingSlipDisabled || readOnly"
				@change="onPackingSlipChange"
			/>
			<label for="packingSlip">
				{{ lang.get('category.form.packing_slip.label') }}
			</label>
			<span v-if="isPackingSlipDisabled" class="learn-more">
				{{ lang.get('features.not_available') }}
				<a href="/feature-disabled/packing_slips">{{ lang.get('shared.learn_more') }}</a>
			</span>
		</div>
		<div v-if="isPackingSlipChecked" class="form-group">
			<label>
				{{ lang.get('category.form.packing_slip_instructions.label') }}
			</label>
			<merge-fields
				:merge-fields="availableMergeFields"
				:help-text="lang.get('category.form.packing_slip_instructions.help')"
			>
				<!-- eslint-disable-next-line vue/no-useless-template-attributes -->
				<template #default="useMergeFields">
					<multilingual
						:supported-languages="supportedLanguages"
						:resource="category"
						property="packingSlipInstructions"
						mode="markdown"
						markdown-mode="full"
						:disabled="readOnly"
						@focus="(element) => useMergeFields.setCaretPosition(element)"
						@keyup="(element) => useMergeFields.setCaretPosition(element)"
						@input="(translated) => handleTranslatedInput('packingSlipInstructions', translated, true, false)"
					/>
				</template>
			</merge-fields>
		</div>
	</div>
</template>

<script>
import { categoryConfiguratorDeliveryController } from '@/modules/entry-form/Configuration/CategoryConfigurator/CategoryConfiguratorDelivery.controller';
import { defineComponent } from 'vue';
import handleInputMixin from '../handle-input-mixin.js';
import handleTranslatedInputMixin from '../handle-translated-input-mixin.js';
import MergeFields from '@/lib/components/Shared/MergeFields/MergeFields';
import Multilingual from '@/lib/components/Translations/Multilingual';

export default defineComponent({
	components: {
		Multilingual,
		MergeFields,
	},
	mixins: [handleInputMixin, handleTranslatedInputMixin],
	props: {
		category: {
			type: Object,
			required: true,
		},
		mergeFields: {
			type: Array,
			default: () => [],
		},
		readOnly: {
			type: Boolean,
			default: false,
		},
	},
	setup(props, { emit }) {
		return categoryConfiguratorDeliveryController(props, emit);
	},
});
</script>
