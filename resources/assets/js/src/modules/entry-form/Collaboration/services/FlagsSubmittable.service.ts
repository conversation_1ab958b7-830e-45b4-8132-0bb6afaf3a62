import { Collections } from '@/domain/services/Rt/DataSource';
import { useCollaborativeSubmittable } from '@/modules/entry-form/Collaboration/services/CollaborativeSubmittable';
import { DocumentService, documentServiceFactory, nullDocumentService } from '@/domain/services/Collaboration/Document';

type FlagsSubmittableService = {
	service: ReturnType<typeof nullDocumentService> | DocumentService;
};
const emptyFlagsSubmittableService = () => ({
	...nullDocumentService(),
});

const flagsSubmittableService = (): FlagsSubmittableService => {
	const { submittableSlug, formSlug, isCollaborative } = useCollaborativeSubmittable();

	if (!isCollaborative) {
		return {
			service: emptyFlagsSubmittableService(),
		};
	}

	return {
		service: documentServiceFactory(submittableSlug, formSlug)(Collections.FlagsSubmittable),
	};
};

export { flagsSubmittableService };
