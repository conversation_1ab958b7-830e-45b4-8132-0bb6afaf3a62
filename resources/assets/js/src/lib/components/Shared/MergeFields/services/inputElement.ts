import { Ref } from 'vue';

const insert = (mergeFieldText: string, lastInputElementInFocus: Ref<HTMLInputElement | null>) => {
	// IE8 and below doesn't have these properties
	if (
		!lastInputElementInFocus.value ||
		(lastInputElementInFocus.value &&
			(!('selectionStart' in lastInputElementInFocus.value) ||
				!('selectionEnd' in lastInputElementInFocus.value)))
	) {
		return;
	}

	// Find the current caret position
	const caretPosition: number = lastInputElementInFocus.value.selectionStart ?? 0;

	// Grab the values of the input and merge field
	const content: string = lastInputElementInFocus.value.value;

	// Insert the merge field value into the textarea
	lastInputElementInFocus.value.value =
		content.substring(0, caretPosition) + mergeFieldText + content.substring(caretPosition);

	// Adjust caret position
	lastInputElementInFocus.value.selectionStart = lastInputElementInFocus.value.selectionEnd =
		caretPosition + mergeFieldText.length;

	// Return focus to the textarea
	lastInputElementInFocus.value.focus();
	lastInputElementInFocus.value.dispatchEvent(new Event('input'));
};

export { insert };
