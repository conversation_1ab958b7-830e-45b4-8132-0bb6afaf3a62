import * as $ from 'jquery'; /** @SimpleMde */
import { Ref } from 'vue';
import { setLastActiveEditor } from '@/lib/components/Shared/MergeFields/services/lastActiveEditor';
import { SimpleMdeEditor } from '@/lib/components/Shared/editor/TextEditor.types';

const insert = (mergeFieldText: string, lastActiveEditor: SimpleMdeEditor) => {
	const editor = lastActiveEditor?.editor;
	const line = lastActiveEditor?.line ?? 0;
	const ch = lastActiveEditor?.ch ?? 0;
	editor.setCursor({ line, ch });
	editor.getDoc().replaceRange(mergeFieldText, editor.getCursor());
	editor.focus();
};

const addListener = (inputElements: Ref<SimpleMdeEditor[]>) => {
	// Listen to the 'mdeFocus' event and track the markdown editor that
	// was last in focus, so that a merge field can be inserted into it later
	if (inputElements.value.length) {
		$(inputElements.value).on('mdeFocus', (e: Event, editor: unknown, line: number, ch: number) => {
			setLastActiveEditor({ editor, line, ch } as SimpleMdeEditor);
		});
	}
};

const removeListener = (inputElements: Ref<SimpleMdeEditor[]>) => {
	// Detach the event handler
	if (inputElements.value.length) {
		$(inputElements.value).off('mdeFocus');
	}
};

export { insert, addListener, removeListener };
