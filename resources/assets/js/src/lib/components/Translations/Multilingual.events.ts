import { TranslationsByLocale } from '@/domain/utils/Translations';

type TranslationValue = string;
type Property = string;
type Language = string;

type MultilingualEmit = {
	(event: 'input', ...args: [TranslationsByLocale, Language, Property, TranslationValue]): void;
	(event: 'focus' | 'keyup', ...args: [HTMLInputElement]): void;
};

// =========================================================================

export { MultilingualEmit };
