import { Lang } from '@/domain/utils/Translations';
import { ComputedRef, Ref } from 'vue';
import { EditorUploadProps, File, FileId } from '@/lib/components/Shared/editor/uploads/Uploads.types';

type NewCommentProps = EditorUploadProps & {
	token: string;
	createUrl: string;
	updateUrl: string;
	translations: object;
};

type NewCommentCreatedResponse = {
	slug: string;
	comment: string; // Full HTML of the comment including comment-container wrapper class etc
	content: string; // Just the comment content
	files: File[];
	relativeTime: string;
	user: string;
	userId: number;
};

type NewCommentRequestPayload = {
	comment: string;
	content: string;
	files: File[];
	relativeTime: string;
	slug: string;
	user: string;
	userId: number;
};

type NewCommentView = {
	lang: Lang;
	useGlobal: (key: string) => unknown;

	commentContent: Ref<string>;
	uploader: Ref<null>;
	disabled: Ref<boolean>;
	files: Ref<File[]>;
	canUpload: ComputedRef<boolean>;
	pendingUploads: ComputedRef<number>;
	createComment: () => void;
	onUpload: () => void;
	onUploadingFile: (file: File) => void;
	onUploadedFile: (completeFileTokens: FileId[], file: File) => void;
	onUploadCompleted: (completeFileTokens: FileId[]) => void;
	onImageDataUrl: (dataUrl: string | null, file: File) => void;
	onUploadCanceled: (file: File) => void;
	onDeletedFile: (file: File) => void;
	hideSaveCommentButton: () => void;
	internal: Ref<boolean>;
	showSaveCommentButton: Ref<boolean>;
};

export { NewCommentCreatedResponse, NewCommentRequestPayload, NewCommentView, NewCommentProps };
