<template>
	<div class="comment-container" :class="{ internal: internal }">
		<!-- View comment -->
		<div v-if="mode === 'view'">
			<div class="clearfix">
				<i class="af-icons af-icons-comment"></i>
				<strong class="comment-name" v-output="commentAuthor"></strong>
				<div v-if="internal" class="badge badge-warning">{{ lang.get('comments.visibility.internal.badge') }}</div>
			</div>
			<div class="comment" :class="{ internal: internal }">
				<div v-if="comment.content && comment.content.length" class="comment-text" v-output="comment.content"></div>
				<div class="comment-update hidden">
					<a v-output="lang.get('buttons.cancel')" href="#" class="btn btn-tertiary btn-sm comment-action-cancel"></a>
				</div>
				<div class="comment-meta">
					{{ comment.relativeTime }}
					<comment-action-buttons
						v-if="!readOnly && userId === comment.userId"
						:mode="mode"
						:comment="comment"
						:disabled="buttonsDisabled"
						:token="token"
						:internal="internal"
						@edit="startedEditing"
						@canceled="onUploadCanceled"
						@delete="showConfirmDeletionModal = true"
						@changed-internal="onChangeInternal"
					/>
				</div>
			</div>
		</div>

		<!-- Edit comment -->
		<div v-if="mode === 'edit' && !readOnly">
			<div class="comment-new">
				<div class="icon-comment-wrapper">
					<i class="af-icons af-icons-comment"></i>
				</div>

				<text-editor
					v-model="commentContent"
					:uploads="uploads"
					:uploader-object="uploaderObject"
					:uploader-options="uploaderOptions"
					:comment="comment"
					@upload="onUpload"
					@uploading="onUploadingFile"
					@imageDataUrl="onImageDataUrl"
					@uploaded="onUploadedFile"
					@completed="onUploadCompleted"
				/>
				<comment-action-buttons
					v-if="userId === comment.userId"
					:mode="mode"
					:comment="comment"
					:disabled="buttonsDisabled"
					:token="token"
					:internal="internal"
					@cancel="cancelEditing"
					@update="finishedEditing"
					@changed-internal="onChangeInternal"
					:enable-internal-comments="enableInternalComments"
				/>
			</div>
		</div>

		<!-- Files uploaded -->
		<comment-files
			v-if="uploads"
			:files="files"
			:deleting-files="deletingFiles"
			:read-only="readOnly"
			:uploader="uploaderObject"
			:uploader-options="uploaderOptions"
			@deleted="onDeletedFile"
		/>
		<uploader
			v-if="canUpload"
			ref="uploader"
			:options="uploaderOptions"
			style="display: none"
			@uploadingFile="onUploadingFile"
			@uploaded="onUploadedFile"
			@imageDataUrl="onImageDataUrl"
			@canceled="onUploadCanceled"
			@completed="onUploadCompleted"
		/>

		<!-- Confirm deletion modal -->
		<confirmation-simple-modal
			v-if="showConfirmDeletionModal"
			:translations="translations"
			@closed="showConfirmDeletionModal = false"
			@confirmed="deleteComment"
		/>
	</div>
</template>

<script lang="ts">
import CommentActionButtons from '@/lib/components/Comments/CommentActionButtons';
import { commentController } from '@/lib/components/Comments/Comment.controller';
import CommentFiles from '@/lib/components/Comments/CommentFiles';
import { CommentView } from '@/lib/components/Comments/Comment.types';
import ConfirmationSimpleModal from '@/lib/components/Shared/ConfirmationSimpleModal';
import { defineComponent } from 'vue';
import TextEditor from '@/lib/components/Shared/editor/TextEditor.vue';
import Uploader from '@/lib/components/Uploader/Uploader';
import { useUploadProps } from '@/lib/components/Shared/editor/uploads/uploadsProps';
import langMixin from '@/lib/components/Translations/mixins/lang-mixin';

export default defineComponent({
	components: {
		Uploader,
		CommentActionButtons,
		CommentFiles,
		ConfirmationSimpleModal,
		TextEditor,
	},
	mixins: [langMixin],
	props: {
		...useUploadProps,
		comment: {
			type: Object,
			required: true,
		},
		token: {
			type: String,
			required: true,
		},
		readOnly: {
			type: Boolean,
			required: true,
		},
		deleteUrl: {
			type: String,
			required: true,
		},
		updateUrl: {
			type: String,
			required: true,
		},
		translations: {
			type: Object,
			default: () => {},
		},
		enableInternalComments: {
			type: Boolean,
			default: false,
		},
	},

	setup: commentController as CommentView,
});
</script>
