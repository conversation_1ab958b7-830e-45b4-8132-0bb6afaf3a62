import { insert } from '@/lib/components/Shared/MergeFields/services/inputElement';
import { describe, expect, it, vi } from 'vitest';
import { Ref, ref } from 'vue';

// Mock for the Ref type
const mockRef = (initialValue: unknown) => {
	const refValue = ref(initialValue);
	return {
		...refValue,
		value: initialValue,
	};
};

vi.stubGlobal(
	'Event',
	class {
		// eslint-disable-next-line no-useless-constructor, @typescript-eslint/no-empty-function
		constructor() {}
	}
);

describe('insert', () => {
	it('should insert the merge field text at the current caret position', () => {
		// Arrange
		const mergeFieldText = '{account_name}';
		const inputElementValue = 'This is a test.';
		const caretPosition = 5;
		const lastInputElementInFocus = mockRef({
			value: inputElementValue as string,
			selectionStart: caretPosition,
			selectionEnd: caretPosition,
			focus: vi.fn(),
			dispatchEvent: vi.fn(),
		}) as Ref<HTMLInputElement>;

		insert(mergeFieldText, lastInputElementInFocus);

		expect(lastInputElementInFocus.value.value).toEqual('This {account_name}is a test.');
		expect(lastInputElementInFocus.value.selectionStart).toEqual(caretPosition + mergeFieldText.length);
		expect(lastInputElementInFocus.value.selectionEnd).toEqual(caretPosition + mergeFieldText.length);
		expect(lastInputElementInFocus.value.focus).toHaveBeenCalled();
		expect(lastInputElementInFocus.value.dispatchEvent).toHaveBeenCalledWith(new Event('input'));
	});

	it('should do nothing if lastInputElementInFocus is null', () => {
		const mergeFieldText = 'Hello';
		const lastInputElementInFocus = mockRef(null) as Ref<HTMLInputElement>;

		insert(mergeFieldText, lastInputElementInFocus);

		expect(lastInputElementInFocus.value).toBeNull();
	});

	it('should do nothing if selectionStart and selectionEnd are not present', () => {
		// Arrange
		const mergeFieldText = 'Hello';
		const lastInputElementInFocus = mockRef({
			value: 'Some value',
			focus: vi.fn(),
			dispatchEvent: vi.fn(),
		}) as Ref<HTMLInputElement>;

		insert(mergeFieldText, lastInputElementInFocus);

		expect(lastInputElementInFocus.value.value).toBe('Some value');
		expect(lastInputElementInFocus.value.focus).not.toHaveBeenCalled();
		expect(lastInputElementInFocus.value.dispatchEvent).not.toHaveBeenCalled();
	});
});
