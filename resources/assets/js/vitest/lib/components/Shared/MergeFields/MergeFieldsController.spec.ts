import { Editor } from '@/lib/components/Shared/editor/TextEditor.types';
import { insert as insertMergeFieldIntoCkeditor } from '@/lib/components/Shared/MergeFields/services/ckeditor';
import { insert as insertMergeFieldIntoInputElement } from '@/lib/components/Shared/MergeFields/services/inputElement';
import { insert as insertMergeFieldIntoIntoSimpleMde } from '@/lib/components/Shared/MergeFields/services/simplemde';
import { lastActiveEditor } from '@/lib/components/Shared/MergeFields/services/lastActiveEditor';
import { ref } from 'vue';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { isEditorCkeditor, isEditorSimpleMde } from '@/lib/components/Shared/editor/helpers/activeEditor';
import { mergeFieldsControllerFactory, Props, View } from '@/lib/components/Shared/MergeFields/MergeFields.controller';

const props: Props = {
	mergeFields: [
		'account_name',
		'first_name',
		'last_name',
		'application_name',
		'application_slug',
		'application_local_id',
	],
	helpText: 'Available merge fields',
	targetElements: ['textarea', 'input'],
};

vi.mock('@/lib/components/Shared/editor/helpers/activeEditor');
vi.mock('@/lib/components/Shared/MergeFields/services/inputElement');
vi.mock('@/lib/components/Shared/MergeFields/services/ckeditor');
vi.mock('@/lib/components/Shared/MergeFields/services/simplemde');

const onMountedHook = vi.fn();
const onBeforeUnmountHook = vi.fn();
const mergeFieldsController = (props: Props): View =>
	mergeFieldsControllerFactory({ onMountedHook, onBeforeUnmountHook })(props);

describe('MergeFieldsController', () => {
	beforeEach(() => {
		vi.resetAllMocks();
		vi.resetModules();
		vi.clearAllMocks();
	});

	it('should format merge field', () => {
		vi.mocked(isEditorSimpleMde).mockReturnValue(false);
		vi.mocked(isEditorCkeditor).mockReturnValue(true);

		const format = mergeFieldsController(props).format('account_name');
		expect(format).toBe('{account_name}');
	});

	it('should bind setCaretPosition method', () => {
		const controller = mergeFieldsController(props);
		const binded = controller.binded.value;

		expect(binded.setCaretPosition).toBeDefined();
		expect(binded.setCaretPosition).toBeInstanceOf(Function);
	});

	// CKEditor
	it('should insert merge field into ckeditor', () => {
		vi.mocked(isEditorCkeditor).mockReturnValue(true);
		vi.mocked(isEditorSimpleMde).mockReturnValue(false);

		const controller = mergeFieldsController(props);
		const formattedMergeField = controller.format('account_name');
		const element = ref('element');
		controller.binded.value.setCaretPosition({ editor: element } as unknown as Editor);
		controller.insert('account_name');

		expect(insertMergeFieldIntoCkeditor).toHaveBeenCalledWith(formattedMergeField, { editor: element });
		expect(insertMergeFieldIntoInputElement).not.toHaveBeenCalled();
		expect(insertMergeFieldIntoIntoSimpleMde).not.toHaveBeenCalled();
	});

	it('should not call onMounted and onBeforeUnmount hooks if editor is Ckeditor', () => {
		vi.mocked(isEditorSimpleMde).mockReturnValue(false);

		mergeFieldsController(props);
		expect(onMountedHook).not.toHaveBeenCalled();
		expect(onBeforeUnmountHook).not.toHaveBeenCalled();
	});

	// SimpleMde

	it('should insert merge field into SimpleMde', () => {
		vi.mocked(isEditorSimpleMde).mockReturnValue(true);
		vi.mocked(isEditorCkeditor).mockReturnValue(false);

		const controller = mergeFieldsController(props);
		const formattedMergeField = controller.format('account_name');
		controller.insert('account_name');

		expect(insertMergeFieldIntoIntoSimpleMde).toHaveBeenCalledWith(formattedMergeField, lastActiveEditor);
		expect(insertMergeFieldIntoInputElement).not.toHaveBeenCalled();
		expect(insertMergeFieldIntoCkeditor).not.toHaveBeenCalled();
	});

	it('should call onMounted and onBeforeUnmount hooks if editor is SimpleMde', () => {
		vi.mocked(isEditorSimpleMde).mockReturnValue(true);

		mergeFieldsController(props);
		expect(onMountedHook).toHaveBeenCalled();
		expect(onBeforeUnmountHook).toHaveBeenCalled();
	});

	// Input elements

	it('should insert merge field into input element', () => {
		const controller = mergeFieldsController(props);
		const formattedMergeField = controller.format('account_name');
		const element = ref('element');
		controller.binded.value.setCaretPosition(element as unknown as HTMLInputElement);
		controller.insert('account_name');

		expect(insertMergeFieldIntoInputElement).toHaveBeenCalledWith(formattedMergeField, { value: element.value });
		expect(insertMergeFieldIntoCkeditor).not.toHaveBeenCalled();
		expect(insertMergeFieldIntoIntoSimpleMde).not.toHaveBeenCalled();
	});
});
