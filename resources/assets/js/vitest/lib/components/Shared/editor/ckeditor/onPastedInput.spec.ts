import { onPastedInput } from '@/lib/components/Shared/editor/ckeditor/on-pasted-input';
import { processDocumentHtml } from '@/lib/components/Shared/editor/ckeditor/document-importer';
import { beforeEach, describe, expect, it, vi } from 'vitest';

vi.mock('@/lib/components/Shared/editor/ckeditor/document-importer', () => ({
	processDocumentHtml: vi.fn((html) => html), // Mocked implementation, returning the same HTML for simplicity
}));

describe('onPastedInput', () => {
	beforeEach(() => {
		vi.resetAllMocks();
	});

	it('should return early if editor is read-only', () => {
		const editor = { isReadOnly: true, data: { htmlProcessor: { toView: vi.fn() } } };
		const data = { dataTransfer: { getData: vi.fn() } };
		const e = {}; // event object, not used in this it

		onPastedInput(e, data, editor);

		expect(processDocumentHtml).not.toHaveBeenCalled();
		expect(editor.data.htmlProcessor.toView).not.toHaveBeenCalled();
	});

	it('should process and set editor content when editor is not read-only', () => {
		const editor = { isReadOnly: false, data: { htmlProcessor: { toView: vi.fn() } } };
		const dataTransferMock = { getData: vi.fn(() => '<p>Pasted HTML</p>') };
		const data = { dataTransfer: dataTransferMock };
		const e = {};

		onPastedInput(e, data, editor);

		expect(processDocumentHtml).toHaveBeenCalledWith('<p>Pasted HTML</p>');
		expect(editor.data.htmlProcessor.toView).toHaveBeenCalledWith('<p>Pasted HTML</p>');
	});

	it('should get pasted text from text/plain if text/html is empty', () => {
		const editor = { isReadOnly: false, data: { htmlProcessor: { toView: vi.fn() } } };
		const getDataMock = vi.fn((type) => (type === 'text/html' ? '' : 'Pasted text'));
		const dataTransferMock = { getData: getDataMock };
		const data = { dataTransfer: dataTransferMock };
		const e = {};

		onPastedInput(e, data, editor);

		expect(getDataMock).toHaveBeenCalledTimes(2);
		expect(getDataMock).toHaveBeenCalledWith('text/html');
		expect(getDataMock).toHaveBeenCalledWith('text/plain');
		expect(processDocumentHtml).toHaveBeenCalledWith('');
		expect(editor.data.htmlProcessor.toView).toHaveBeenCalledWith('Pasted text');
	});
});
