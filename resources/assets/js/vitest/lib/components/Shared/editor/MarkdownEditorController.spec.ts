import { EditorEmit } from '@/lib/components/Shared/editor/EditorEvents';
import { EditorProps } from '@/lib/components/Shared/editor/TextEditor.types';
import { textEditorController } from '@/lib/components/Shared/editor/TextEditor.controller';
import { afterEach, describe, expect, it, vi } from 'vitest';

const emit: EditorEmit = <EditorEmit>(<unknown>vi.fn());
const props: EditorProps = {};

describe('markdownEditorController', () => {
	afterEach(() => {
		vi.clearAllMocks();
	});

	it('should emit "input" when onInput is called', () => {
		const controller = textEditorController(props, emit);
		controller.onInput('test');
		expect(emit).toHaveBeenCalledWith('input', 'test');
	});

	it('should emit "blur" when onBlur is called', () => {
		const controller = textEditorController(props, emit);
		controller.onBlur();
		expect(emit).toHaveBeenCalledWith('blur');
	});

	it('should emit "upload" when onUploadFile is called', () => {
		const controller = textEditorController(props, emit);
		controller.onUploadFile();
		expect(emit).toHaveBeenCalledWith('upload');
	});

	it('should emit "focus" when onFocus is called', () => {
		const controller = textEditorController(props, emit);
		controller.onFocus('test' as unknown as HTMLInputElement);
		expect(emit).toHaveBeenCalledWith('focus', 'test');
	});

	it('should emit "keyup" when onKeyup is called', () => {
		const controller = textEditorController(props, emit);
		controller.onKeyup('test' as unknown as HTMLInputElement);
		expect(emit).toHaveBeenCalledWith('keyup', 'test');
	});
});
