import { customToolbar, mediaEmbed, toolbar } from '@/lib/components/Shared/editor/ckeditor/toolbar/buttons';
import { describe, expect, it } from 'vitest';

describe('toolbar buttons', () => {
	it('should return the default toolbar', () => {
		expect(customToolbar({})).toEqual(toolbar);
	});

	it('should return the toolbar with mediaEmbed', () => {
		expect(customToolbar({ toolbarMediaEmbed: true })).toEqual([...toolbar, ...mediaEmbed]);
	});
});
