import { processDocumentHtml } from '@/lib/components/Shared/editor/ckeditor/document-importer';
import { describe, expect, it, vi } from 'vitest';

// Mock the handleImages function since it is imported from another module
vi.mock('@/lib/components/Shared/editor/ckeditor/document-images-importer', () => ({
	handleImages: (html: string) => html,
}));

// temporary adds polyfill for String.prototype.replaceAll function since it is not supported by node 14, this can be removed when node 16 is used
if (!String.prototype.replaceAll) {
	// eslint-disable-next-line no-extend-native
	String.prototype.replaceAll = function (searchValue, replaceValue) {
		return this.replace(new RegExp(searchValue, 'g'), <string>replaceValue);
	};
}

describe('document importer', () => {
	it('should remove everything before the body tag', () => {
		const inputHtml = '<html><head></head><body><p>Hello</p></body></html>';
		const expectedOutput = '<body><p>Hello</p></body></html>';
		expect(processDocumentHtml(inputHtml)).toEqual(expectedOutput);
	});

	it('should remove google docs bold wrapper', () => {
		const inputHtml = '<body><b>Hello</b><b style="font-weight:normal;">bold</b></body>';
		const expectedOutput = '<body><b>Hello</b>bold</body>';
		expect(processDocumentHtml(inputHtml)).toEqual(expectedOutput);
	});

	it('should remove embedded google docs paragraph tags in list items', () => {
		const inputHtml =
			'<li blabla:bobo>' +
			'<p dir="ltr" style="line-height:1.38;margin-top:0pt;margin-bottom:0pt;" role="presentation"><span style="font-size:12pt">XXA-One</span></p></li>' +
			'<li>test</li>';
		const expectedOutput = '<li><span style="font-size:12pt">XXA-One</span></li><li>test</li>';
		expect(processDocumentHtml(inputHtml)).toEqual(expectedOutput);
	});

	it('should remove empty span tags', () => {
		const inputHtml =
			'Is_there_a_space_preserved_here-><span style="vertical-align:baseline;white-space:pre;white-space:pre-wrap;"> </span><-Hmm?</p>\n' +
			'\n' +
			'<p dir="ltr" style="line-height:1.38;text-align: center;margin-top:0pt;margin-bottom:0pt;">\n' +
			'\t\t<span style="font-size:12pt;font-family:Arial;color:#000000;background-color:transparent;font-weight:400;' +
			'font-style:normal;font-variant:normal;text-decoration:none;vertical-align:baseline;white-space:pre;white-space:pre-wrap;"/>\n' +
			'\t</p>';

		const expectedOutput =
			'Is_there_a_space_preserved_here-> <-Hmm?</p>\n' +
			'\n' +
			'<p dir="ltr" style="line-height:1.38;text-align: center;margin-top:0pt;margin-bottom:0pt;">\n' +
			'\t\t \n' +
			'\t</p>';
		expect(processDocumentHtml(inputHtml)).toEqual(expectedOutput);
	});

	it('should remove double spaces', () => {
		const inputHtml = '<p>Some  text</p>';
		const expectedOutput = '<p>Some text</p>';
		expect(processDocumentHtml(inputHtml)).toEqual(expectedOutput);
	});

	it('should remove blank lines', () => {
		const inputHtml = '<p>Some text</p><br><br><p>Another line</p>';
		const expectedOutput = '<p>Some text</p><p>Another line</p>';
		expect(processDocumentHtml(inputHtml)).toEqual(expectedOutput);
	});
});
