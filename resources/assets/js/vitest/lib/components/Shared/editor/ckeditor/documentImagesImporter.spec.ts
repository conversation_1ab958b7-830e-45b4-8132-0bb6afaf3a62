import { handleImages } from '@/lib/components/Shared/editor/ckeditor/document-images-importer';
import { describe, expect, it } from 'vitest';

describe('imageElements', () => {
	it('should remove the image element if the src contains "file://"', () => {
		const html = '<div><img src="file://some-image.jpg"/><p>Some text<img src="image2.jpg"/></p></div>';

		expect(handleImages(html)).toEqual('<div><p>Some text<img src="image2.jpg"/></p></div>');
	});

	it('should preserve width and height attributes', () => {
		const html = '<div><img src="image1.jpg" width="100" height="200"/><p>Some text<img src="image2.jpg"/></p></div>';

		expect(handleImages(html)).toEqual(
			'<div><img src="image1.jpg" style="width:100px;height:200px"><p>Some text<img src="image2.jpg"/></p></div>'
		);
	});
});
