<script>
    var currentBreadcrumb = '{{ $currentBreadcrumb ?? url()->current() }}'
    var App = {
        accountName: "{{ $account->name }}",
        language: "{{ $language }}",
        defaultLanguage: "{{ $defaultLanguage }}",
        defaultTimezone: "{{ $defaultTimezone }}",
        supportedLanguages: "{{ obfuscate($supportedLanguages) }}",
        preferredLanguage: "{{ $preferredLanguage }}",
        country: "{{ detect_country() }}",
        editor: "{{ feature_enabled('ckeditor') ? 'ckeditor' : 'simplemde' }}",
        momentLocale: "{{ $momentLocale }}",
        momentDateFormat: "{{ $momentDateFormat }}",
        momentTimeFormat: "{{ $momentTimeFormat }}",
        timezones: "{{ obfuscate($timezones) }}",
        selectedItemsMessage: "{{ obfuscate(trans('miscellaneous.selected_items')) }}",
        region: "{{ $account->region }}",
        formulaFieldCompatibleTypes: "{{ obfuscate($formulaFieldCompatibleTypes) }}",
        hyperFormulaLicense: "{{ obfuscate($hyperFormulaLicense) }}",
        currentBreadcrumb: currentBreadcrumb || null,
        app: "{{current_account()->brand}}",
        obfuscatedData: "{{ VueData::getObfuscatedData() }}",
        consumer: "{{ obfuscate($vueConsumer) }}",
        pjaxSelector: "{{ obfuscate($pjaxSelector ?? '#content a:not(.ignore):not(a[target="_blank"]):not([data-redirector]), #navigation a:not(.ignore), .tray-content a:not(.ignore):not(a[target="_blank"]), .pjax-anchor') }}",
        flashMessage: "{{ obfuscate($flashMessage) }}",
        validationErrors: "{{ obfuscate($validationErrors) }}",
        regions: "{{ obfuscate(countries_regions()) }}",
    };
</script>
